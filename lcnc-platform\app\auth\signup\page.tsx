"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useAuth } from "@/components/auth/auth-context"
import { useProjects } from "@/components/projects/project-context"
import Link from "next/link"

export default function SignUpPage() {
  const router = useRouter()
  const { signup } = useAuth()
  const { createProject } = useProjects()
  const [step, setStep] = useState(1)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [formData, setFormData] = useState({
    creatorName: "",
    email: "",
    projectName: "",
    description: "",
  })

  const validateStep1 = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.creatorName.trim()) {
      newErrors.creatorName = "Creator name is required"
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const validateStep2 = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.projectName.trim()) {
      newErrors.projectName = "Project name is required"
    }

    if (!formData.description.trim()) {
      newErrors.description = "Project description is required"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleGetStarted = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateStep1()) {
      setStep(2)
    }
  }

  const handleNext = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateStep2()) return

    setIsLoading(true)

    try {
      // Create user account
      const signupSuccess = await signup(formData)

      if (signupSuccess) {
        // Create the project
        await createProject({
          name: formData.projectName,
          description: formData.description,
          creatorId: Date.now().toString(), // This would come from the user context in a real app
          creatorName: formData.creatorName,
          creatorEmail: formData.email,
        })

        // Redirect to dashboard
        router.push("/dashboard")
      } else {
        setErrors({ general: "Failed to create account. Please try again." })
      }
    } catch (error) {
      console.error("Signup error:", error)
      setErrors({ general: "An unexpected error occurred. Please try again." })
    } finally {
      setIsLoading(false)
    }
  }

  const handleBack = () => {
    setStep(1)
    setErrors({})
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-100 via-purple-50 to-purple-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="bg-gray-800 rounded-2xl p-8 shadow-2xl">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-semibold text-white">Create A New Project</h1>
            <div className="flex justify-center mt-4 space-x-2">
              <div className={`w-3 h-3 rounded-full ${step >= 1 ? "bg-blue-500" : "bg-gray-600"}`} />
              <div className={`w-3 h-3 rounded-full ${step >= 2 ? "bg-blue-500" : "bg-gray-600"}`} />
            </div>
          </div>

          {errors.general && (
            <div className="mb-4 p-3 bg-red-500/10 border border-red-500/20 rounded-lg">
              <p className="text-red-400 text-sm">{errors.general}</p>
            </div>
          )}

          {step === 1 ? (
            <form onSubmit={handleGetStarted} className="space-y-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-white text-sm font-medium mb-2">Creator Name</label>
                  <Input
                    type="text"
                    placeholder="Name..."
                    value={formData.creatorName}
                    onChange={(e) => {
                      setFormData({ ...formData, creatorName: e.target.value })
                      if (errors.creatorName) setErrors({ ...errors, creatorName: "" })
                    }}
                    className={`bg-gray-700 border-gray-600 text-white placeholder-gray-400 h-12 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.creatorName ? "border-red-500" : ""
                    }`}
                  />
                  {errors.creatorName && <p className="text-red-400 text-xs mt-1">{errors.creatorName}</p>}
                </div>

                <div>
                  <label className="block text-white text-sm font-medium mb-2">Email</label>
                  <Input
                    type="email"
                    placeholder="Email"
                    value={formData.email}
                    onChange={(e) => {
                      setFormData({ ...formData, email: e.target.value })
                      if (errors.email) setErrors({ ...errors, email: "" })
                    }}
                    className={`bg-gray-700 border-gray-600 text-white placeholder-gray-400 h-12 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.email ? "border-red-500" : ""
                    }`}
                  />
                  {errors.email && <p className="text-red-400 text-xs mt-1">{errors.email}</p>}
                </div>
              </div>

              <Button
                type="submit"
                className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
              >
                Get Started
              </Button>
            </form>
          ) : (
            <form onSubmit={handleNext} className="space-y-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-white text-sm font-medium mb-2">Name Your Project</label>
                  <Input
                    type="text"
                    placeholder="Enter project name..."
                    value={formData.projectName}
                    onChange={(e) => {
                      setFormData({ ...formData, projectName: e.target.value })
                      if (errors.projectName) setErrors({ ...errors, projectName: "" })
                    }}
                    className={`bg-gray-700 border-gray-600 text-white placeholder-gray-400 h-12 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                      errors.projectName ? "border-red-500" : ""
                    }`}
                  />
                  {errors.projectName && <p className="text-red-400 text-xs mt-1">{errors.projectName}</p>}
                </div>

                <div>
                  <label className="block text-white text-sm font-medium mb-2">Application Description</label>
                  <Textarea
                    placeholder="Describe your app. The Industry's Standard Dummy..."
                    value={formData.description}
                    onChange={(e) => {
                      setFormData({ ...formData, description: e.target.value })
                      if (errors.description) setErrors({ ...errors, description: "" })
                    }}
                    className={`bg-gray-700 border-gray-600 text-white placeholder-gray-400 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent min-h-[120px] resize-none ${
                      errors.description ? "border-red-500" : ""
                    }`}
                  />
                  {errors.description && <p className="text-red-400 text-xs mt-1">{errors.description}</p>}
                </div>
              </div>

              <div className="flex gap-3">
                <Button
                  type="button"
                  onClick={handleBack}
                  variant="outline"
                  className="flex-1 h-12 bg-transparent border-gray-600 text-gray-300 hover:bg-gray-700 font-medium rounded-lg transition-colors"
                >
                  Back
                </Button>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="flex-1 h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors flex items-center justify-center gap-2"
                >
                  {isLoading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/20 border-t-white rounded-full animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      Next
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                        <path d="M5 12h14M12 5l7 7-7 7" />
                      </svg>
                    </>
                  )}
                </Button>
              </div>
            </form>
          )}

          <div className="mt-6 text-center">
            <p className="text-gray-400 text-sm">
              Already have an account?{" "}
              <Link href="/auth/signin" className="text-blue-400 hover:text-blue-300">
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
