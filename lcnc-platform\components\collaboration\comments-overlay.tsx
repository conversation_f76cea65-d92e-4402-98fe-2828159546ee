"use client"

import type React from "react"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { MessageCircle, Check, X } from "lucide-react"
import { useCollaboration } from "./collaboration-context"

export function CommentsOverlay() {
  const { comments, addComment, resolveComment } = useCollaboration()
  const [newComment, setNewComment] = useState({ content: "", x: 0, y: 0, show: false })

  const handleCanvasClick = (e: React.MouseEvent) => {
    if (e.detail === 2) {
      // Double click to add comment
      const rect = e.currentTarget.getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top
      setNewComment({ content: "", x, y, show: true })
    }
  }

  const handleAddComment = () => {
    if (newComment.content.trim()) {
      addComment(newComment.content, newComment.x, newComment.y)
      setNewComment({ content: "", x: 0, y: 0, show: false })
    }
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })
  }

  return (
    <div className="absolute inset-0 pointer-events-none">
      {/* Canvas click handler */}
      <div className="absolute inset-0 pointer-events-auto" onClick={handleCanvasClick} />

      {/* Existing Comments */}
      {comments
        .filter((comment) => !comment.resolved)
        .map((comment) => (
          <div
            key={comment.id}
            className="absolute pointer-events-auto z-40"
            style={{ left: comment.x, top: comment.y }}
          >
            {/* Comment Pin */}
            <div
              className="w-6 h-6 rounded-full flex items-center justify-center shadow-lg cursor-pointer group"
              style={{ backgroundColor: comment.user.color }}
            >
              <MessageCircle size={12} className="text-white" />
            </div>

            {/* Comment Popup */}
            <div className="absolute top-8 left-0 w-64 bg-white rounded-lg shadow-xl border border-gray-200 p-3 opacity-0 group-hover:opacity-100 transition-opacity">
              <div className="flex items-start gap-2 mb-2">
                <div
                  className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium"
                  style={{ backgroundColor: comment.user.color }}
                >
                  {comment.user.name.charAt(0)}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm text-gray-900">{comment.user.name}</div>
                  <div className="text-xs text-gray-500">{formatTime(comment.createdAt)}</div>
                </div>
              </div>
              <p className="text-sm text-gray-700 mb-3">{comment.content}</p>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => resolveComment(comment.id)}
                  className="h-7 px-2 text-xs"
                >
                  <Check size={12} className="mr-1" />
                  Resolve
                </Button>
              </div>
            </div>
          </div>
        ))}

      {/* New Comment Form */}
      {newComment.show && (
        <div className="absolute pointer-events-auto z-50" style={{ left: newComment.x, top: newComment.y }}>
          <div className="w-64 bg-white rounded-lg shadow-xl border border-gray-200 p-3">
            <Textarea
              placeholder="Add a comment..."
              value={newComment.content}
              onChange={(e) => setNewComment({ ...newComment, content: e.target.value })}
              className="mb-3 min-h-[60px] resize-none"
              autoFocus
            />
            <div className="flex gap-2">
              <Button size="sm" onClick={handleAddComment} className="h-7 px-3 text-xs">
                Add Comment
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => setNewComment({ content: "", x: 0, y: 0, show: false })}
                className="h-7 px-3 text-xs"
              >
                <X size={12} />
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
