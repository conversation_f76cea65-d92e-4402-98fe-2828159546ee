import React, { useState, useEffect } from 'react';
import { Settings, Type, Palette, Layout } from 'lucide-react';

const PropertiesPanel = ({ selectedComponent, onUpdateComponent }) => {
  const [properties, setProperties] = useState({});
  const [position, setPosition] = useState({});

  useEffect(() => {
    if (selectedComponent) {
      setProperties(selectedComponent.properties || {});
      setPosition(selectedComponent.position || {});
    } else {
      setProperties({});
      setPosition({});
    }
  }, [selectedComponent]);

  const handlePropertyChange = (key, value) => {
    const newProperties = { ...properties, [key]: value };
    setProperties(newProperties);
    
    if (selectedComponent) {
      onUpdateComponent(selectedComponent.id, { properties: newProperties });
    }
  };

  const handlePositionChange = (key, value) => {
    const newPosition = { ...position, [key]: parseInt(value) || 0 };
    setPosition(newPosition);
    
    if (selectedComponent) {
      onUpdateComponent(selectedComponent.id, { position: newPosition });
    }
  };

  const handleFieldNameChange = (value) => {
    if (selectedComponent) {
      onUpdateComponent(selectedComponent.id, { field_name: value });
    }
  };

  const renderPropertyInputs = () => {
    if (!selectedComponent) return null;

    const { field_type } = selectedComponent;

    switch (field_type) {
      case 'text':
      case 'email':
      case 'number':
        return (
          <>
            <div>
              <label className="block text-gray-400 text-xs font-medium mb-2">Placeholder</label>
              <input
                type="text"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
                value={properties.placeholder || ''}
                onChange={(e) => handlePropertyChange('placeholder', e.target.value)}
              />
            </div>
            <div>
              <label className="block text-gray-400 text-xs font-medium mb-2">Max Length</label>
              <input
                type="number"
                className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
                value={properties.maxLength || ''}
                onChange={(e) => handlePropertyChange('maxLength', parseInt(e.target.value))}
              />
            </div>
            <div>
              <label className="flex items-center text-gray-300 text-sm">
                <input
                  type="checkbox"
                  checked={properties.required || false}
                  onChange={(e) => handlePropertyChange('required', e.target.checked)}
                  className="mr-2 bg-gray-700 border-gray-600 text-blue-500 focus:ring-blue-500"
                />
                Required
              </label>
            </div>
          </>
        );

      case 'textarea':
        return (
          <>
            <div className="form-group">
              <label className="form-label">Placeholder</label>
              <input
                type="text"
                className="form-input"
                value={properties.placeholder || ''}
                onChange={(e) => handlePropertyChange('placeholder', e.target.value)}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Rows</label>
              <input
                type="number"
                className="form-input"
                value={properties.rows || 3}
                onChange={(e) => handlePropertyChange('rows', parseInt(e.target.value))}
              />
            </div>
            <div className="form-group">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={properties.required || false}
                  onChange={(e) => handlePropertyChange('required', e.target.checked)}
                  className="mr-2"
                />
                Required
              </label>
            </div>
          </>
        );

      case 'dropdown':
        return (
          <>
            <div className="form-group">
              <label className="form-label">Options (one per line)</label>
              <textarea
                className="form-textarea"
                rows={4}
                value={(properties.options || []).join('\n')}
                onChange={(e) => handlePropertyChange('options', e.target.value.split('\n').filter(opt => opt.trim()))}
              />
            </div>
            <div className="form-group">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={properties.multiple || false}
                  onChange={(e) => handlePropertyChange('multiple', e.target.checked)}
                  className="mr-2"
                />
                Multiple Selection
              </label>
            </div>
            <div className="form-group">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={properties.required || false}
                  onChange={(e) => handlePropertyChange('required', e.target.checked)}
                  className="mr-2"
                />
                Required
              </label>
            </div>
          </>
        );

      case 'checkbox':
        return (
          <>
            <div className="form-group">
              <label className="form-label">Label</label>
              <input
                type="text"
                className="form-input"
                value={properties.label || ''}
                onChange={(e) => handlePropertyChange('label', e.target.value)}
              />
            </div>
            <div className="form-group">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={properties.checked || false}
                  onChange={(e) => handlePropertyChange('checked', e.target.checked)}
                  className="mr-2"
                />
                Default Checked
              </label>
            </div>
          </>
        );

      case 'radio':
        return (
          <>
            <div className="form-group">
              <label className="form-label">Options (one per line)</label>
              <textarea
                className="form-textarea"
                rows={4}
                value={(properties.options || []).join('\n')}
                onChange={(e) => handlePropertyChange('options', e.target.value.split('\n').filter(opt => opt.trim()))}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Default Value</label>
              <input
                type="text"
                className="form-input"
                value={properties.defaultValue || ''}
                onChange={(e) => handlePropertyChange('defaultValue', e.target.value)}
              />
            </div>
          </>
        );

      case 'button':
        return (
          <>
            <div className="form-group">
              <label className="form-label">Button Text</label>
              <input
                type="text"
                className="form-input"
                value={properties.text || ''}
                onChange={(e) => handlePropertyChange('text', e.target.value)}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Variant</label>
              <select
                className="form-input"
                value={properties.variant || 'primary'}
                onChange={(e) => handlePropertyChange('variant', e.target.value)}
              >
                <option value="primary">Primary</option>
                <option value="secondary">Secondary</option>
                <option value="danger">Danger</option>
                <option value="outline">Outline</option>
              </select>
            </div>
            <div className="form-group">
              <label className="form-label">Action</label>
              <select
                className="form-input"
                value={properties.action || 'submit'}
                onChange={(e) => handlePropertyChange('action', e.target.value)}
              >
                <option value="submit">Submit</option>
                <option value="reset">Reset</option>
                <option value="button">Button</option>
              </select>
            </div>
          </>
        );

      case 'image':
        return (
          <>
            <div className="form-group">
              <label className="form-label">Image URL</label>
              <input
                type="url"
                className="form-input"
                value={properties.src || ''}
                onChange={(e) => handlePropertyChange('src', e.target.value)}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Alt Text</label>
              <input
                type="text"
                className="form-input"
                value={properties.alt || ''}
                onChange={(e) => handlePropertyChange('alt', e.target.value)}
              />
            </div>
          </>
        );

      default:
        return (
          <div className="text-sm text-gray-500">
            No specific properties for this component type.
          </div>
        );
    }
  };

  if (!selectedComponent) {
    return (
      <div className="mt-8">
        <div className="text-center text-gray-400 py-8">
          <div className="w-12 h-12 bg-gray-700 rounded-lg flex items-center justify-center mx-auto mb-4">
            <Settings size={24} className="text-gray-500" />
          </div>
          <h3 className="text-white text-sm font-medium mb-2">No Component Selected</h3>
          <p className="text-gray-400 text-xs">
            Select a component on the canvas to edit its properties
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="mt-8 space-y-6">
      {/* Basic Properties */}
      <div>
        <h3 className="text-white text-sm font-medium mb-4 flex items-center">
          <Type size={16} className="mr-2" />
          Properties
        </h3>

        <div className="space-y-4">
          <div>
            <label className="block text-gray-400 text-xs font-medium mb-2">Component Name</label>
            <input
              type="text"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
              value={selectedComponent.field_name || ''}
              onChange={(e) => handleFieldNameChange(e.target.value)}
            />
          </div>

          <div>
            <label className="block text-gray-400 text-xs font-medium mb-2">Component Type</label>
            <input
              type="text"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-gray-400 text-sm"
              value={selectedComponent.field_type || ''}
              disabled
            />
          </div>
        </div>
      </div>

      {/* Component-specific Properties */}
      <div>
        <h3 className="text-white text-sm font-medium mb-4 flex items-center">
          <Palette size={16} className="mr-2" />
          Settings
        </h3>

        <div className="space-y-4">
          {renderPropertyInputs()}
        </div>
      </div>

      {/* Layout Properties */}
      <div>
        <h3 className="text-white text-sm font-medium mb-4 flex items-center">
          <Layout size={16} className="mr-2" />
          Position & Size
        </h3>

        <div className="grid grid-cols-2 gap-3">
          <div>
            <label className="block text-gray-400 text-xs font-medium mb-2">X</label>
            <input
              type="number"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
              value={position.x || 0}
              onChange={(e) => handlePositionChange('x', e.target.value)}
            />
          </div>

          <div>
            <label className="block text-gray-400 text-xs font-medium mb-2">Y</label>
            <input
              type="number"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
              value={position.y || 0}
              onChange={(e) => handlePositionChange('y', e.target.value)}
            />
          </div>

          <div>
            <label className="block text-gray-400 text-xs font-medium mb-2">Width</label>
            <input
              type="number"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
              value={position.width || 200}
              onChange={(e) => handlePositionChange('width', e.target.value)}
            />
          </div>

          <div>
            <label className="block text-gray-400 text-xs font-medium mb-2">Height</label>
            <input
              type="number"
              className="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-white text-sm focus:border-blue-500 focus:outline-none"
              value={position.height || 40}
              onChange={(e) => handlePositionChange('height', e.target.value)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertiesPanel;
