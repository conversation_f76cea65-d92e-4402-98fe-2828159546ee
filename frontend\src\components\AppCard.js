import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Edit, Trash2 } from 'lucide-react';

const AppCard = ({ app, onDelete }) => {
  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'published':
        return 'bg-green-100 text-green-800';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800';
      case 'archived':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="relative group">
      {/* Glow effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      <div className="relative card group-hover:scale-105 transition-all duration-500 hover:shadow-3xl border-2 border-white/10 hover:border-blue-400/30 overflow-hidden">
        {/* Status Badge */}
        <div className="absolute top-4 right-4 z-10">
          <span className={`inline-flex items-center px-4 py-2 rounded-full text-xs font-bold shadow-xl backdrop-blur-sm ${getStatusColor(app.status)}`}>
            {app.status.toUpperCase()}
          </span>
        </div>

        {/* App Icon/Avatar */}
        <div className="mb-6">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl mb-4">
            <span className="text-white font-bold text-xl">{app.name.charAt(0).toUpperCase()}</span>
          </div>
          <h3 className="text-2xl font-bold text-gray-800 mb-2 truncate group-hover:text-blue-600 transition-colors leading-tight">
            {app.name}
          </h3>
        </div>

        {/* Description */}
        <div className="mb-8">
          <p className="text-gray-600 text-base mb-4 line-clamp-3 leading-relaxed min-h-[4.5rem]">
            {app.description || 'No description provided for this application yet.'}
          </p>
        </div>

        {/* Metadata */}
        <div className="mb-8">
          <div className="flex items-center text-sm text-gray-500 bg-gradient-to-r from-gray-50 to-gray-100 px-4 py-3 rounded-xl border border-gray-200">
            <Calendar size={16} className="mr-3 text-blue-500" />
            <span className="font-medium">Created {formatDate(app.createdAt)}</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-3 pt-6 border-t border-gray-100">
          <Link
            to={`/app/${app.id}`}
            className="btn btn-primary flex-1 py-3 text-base font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
          >
            <Edit size={18} />
            Open Builder
          </Link>

          <button
            onClick={() => onDelete(app.id)}
            className="btn btn-outline p-3 text-red-600 hover:text-red-700 hover:border-red-300 hover:bg-red-50 shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
            title="Delete app"
          >
            <Trash2 size={18} />
          </button>
        </div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
      </div>
    </div>
  );
};

export default AppCard;
