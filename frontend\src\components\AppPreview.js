import React, { useState } from 'react';
import { X, Smartphone, Monitor, Tablet } from 'lucide-react';

const AppPreview = ({ app, components, onClose }) => {
  const [viewMode, setViewMode] = useState('desktop'); // desktop, tablet, mobile

  const getViewportStyles = () => {
    switch (viewMode) {
      case 'mobile':
        return { width: '375px', height: '667px' };
      case 'tablet':
        return { width: '768px', height: '1024px' };
      default:
        return { width: '100%', height: '100%' };
    }
  };

  const renderFormComponent = (component) => {
    const baseStyles = "w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200";
    
    switch (component.field_type) {
      case 'text':
        return (
          <input
            type="text"
            placeholder={component.placeholder || `Enter ${component.field_name}`}
            className={baseStyles}
            defaultValue={component.default_value || ''}
          />
        );
      case 'email':
        return (
          <input
            type="email"
            placeholder={component.placeholder || 'Enter email address'}
            className={baseStyles}
            defaultValue={component.default_value || ''}
          />
        );
      case 'password':
        return (
          <input
            type="password"
            placeholder={component.placeholder || 'Enter password'}
            className={baseStyles}
            defaultValue={component.default_value || ''}
          />
        );
      case 'textarea':
        return (
          <textarea
            placeholder={component.placeholder || `Enter ${component.field_name}`}
            className={`${baseStyles} min-h-[100px] resize-vertical`}
            defaultValue={component.default_value || ''}
          />
        );
      case 'select':
        const options = component.options || ['Option 1', 'Option 2', 'Option 3'];
        return (
          <select className={baseStyles} defaultValue={component.default_value || ''}>
            <option value="">Select an option</option>
            {options.map((option, index) => (
              <option key={index} value={option}>
                {option}
              </option>
            ))}
          </select>
        );
      case 'checkbox':
        return (
          <label className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              className="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              defaultChecked={component.default_value === 'true'}
            />
            <span className="text-gray-700">{component.field_name}</span>
          </label>
        );
      case 'radio':
        const radioOptions = component.options || ['Option 1', 'Option 2'];
        return (
          <div className="space-y-2">
            {radioOptions.map((option, index) => (
              <label key={index} className="flex items-center space-x-3 cursor-pointer">
                <input
                  type="radio"
                  name={component.field_name}
                  value={option}
                  className="w-5 h-5 text-blue-600 border-gray-300 focus:ring-blue-500"
                  defaultChecked={component.default_value === option}
                />
                <span className="text-gray-700">{option}</span>
              </label>
            ))}
          </div>
        );
      case 'button':
        return (
          <button
            type={component.button_type || 'button'}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 font-medium"
          >
            {component.field_name || 'Button'}
          </button>
        );
      case 'heading':
        const HeadingTag = component.heading_level || 'h2';
        return (
          <HeadingTag className="text-2xl font-bold text-gray-900 mb-4">
            {component.field_name || 'Heading'}
          </HeadingTag>
        );
      case 'paragraph':
        return (
          <p className="text-gray-700 leading-relaxed">
            {component.content || component.field_name || 'This is a paragraph of text.'}
          </p>
        );
      case 'image':
        return (
          <div className="w-full h-48 bg-gray-200 rounded-lg flex items-center justify-center">
            {component.src ? (
              <img
                src={component.src}
                alt={component.alt || component.field_name}
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <div className="text-center text-gray-500">
                <div className="text-4xl mb-2">🖼️</div>
                <p>Image Placeholder</p>
              </div>
            )}
          </div>
        );
      default:
        return (
          <div className="p-4 bg-gray-100 border border-gray-300 rounded-lg text-center text-gray-600">
            {component.field_type} component
          </div>
        );
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-7xl h-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <h2 className="text-2xl font-bold text-gray-900">Preview: {app?.name}</h2>
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('desktop')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'desktop' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-200'
                }`}
                title="Desktop View"
              >
                <Monitor size={18} />
              </button>
              <button
                onClick={() => setViewMode('tablet')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'tablet' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-200'
                }`}
                title="Tablet View"
              >
                <Tablet size={18} />
              </button>
              <button
                onClick={() => setViewMode('mobile')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'mobile' ? 'bg-blue-600 text-white' : 'text-gray-600 hover:bg-gray-200'
                }`}
                title="Mobile View"
              >
                <Smartphone size={18} />
              </button>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X size={24} />
          </button>
        </div>

        {/* Preview Content */}
        <div className="flex-1 bg-gray-100 p-8 overflow-auto">
          <div className="flex justify-center">
            <div
              className="bg-white rounded-lg shadow-lg overflow-auto"
              style={getViewportStyles()}
            >
              <div className="p-8 space-y-6">
                {components.length === 0 ? (
                  <div className="text-center py-16">
                    <div className="text-6xl mb-4">📱</div>
                    <h3 className="text-xl font-medium text-gray-600 mb-2">No Components</h3>
                    <p className="text-gray-500">Add components to see your app preview</p>
                  </div>
                ) : (
                  components
                    .sort((a, b) => (a.position?.y || 0) - (b.position?.y || 0))
                    .map((component) => (
                      <div key={component.id} className="space-y-2">
                        {component.field_name && component.field_type !== 'button' && (
                          <label className="block text-sm font-medium text-gray-700">
                            {component.field_name}
                            {component.required && <span className="text-red-500 ml-1">*</span>}
                          </label>
                        )}
                        {renderFormComponent(component)}
                      </div>
                    ))
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {components.length} component{components.length !== 1 ? 's' : ''} • {viewMode} view
            </div>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Close Preview
              </button>
              <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Export App
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppPreview;
