import React from 'react';

const ComponentRenderer = ({ component, isSelected, onUpdate }) => {
  const { field_type, field_name, properties } = component;

  const baseClasses = `w-full h-full border rounded transition-all ${
    isSelected 
      ? 'border-blue-500 shadow-md' 
      : 'border-gray-300 hover:border-gray-400'
  }`;

  const renderComponent = () => {
    switch (field_type) {
      case 'text':
        return (
          <input
            type="text"
            placeholder={properties.placeholder || 'Enter text...'}
            className={`${baseClasses} px-3 py-2 text-sm`}
            disabled
          />
        );

      case 'textarea':
        return (
          <textarea
            placeholder={properties.placeholder || 'Enter text...'}
            className={`${baseClasses} px-3 py-2 text-sm resize-none`}
            disabled
          />
        );

      case 'number':
        return (
          <input
            type="number"
            placeholder={properties.placeholder || 'Enter number...'}
            className={`${baseClasses} px-3 py-2 text-sm`}
            disabled
          />
        );

      case 'email':
        return (
          <input
            type="email"
            placeholder={properties.placeholder || 'Enter email...'}
            className={`${baseClasses} px-3 py-2 text-sm`}
            disabled
          />
        );

      case 'date':
        return (
          <input
            type="date"
            className={`${baseClasses} px-3 py-2 text-sm`}
            disabled
          />
        );

      case 'dropdown':
        return (
          <select className={`${baseClasses} px-3 py-2 text-sm`} disabled>
            <option>{properties.placeholder || 'Select option...'}</option>
            {(properties.options || []).map((option, index) => (
              <option key={index} value={option}>
                {option}
              </option>
            ))}
          </select>
        );

      case 'checkbox':
        return (
          <label className={`${baseClasses} flex items-center px-3 py-2 text-sm cursor-pointer`}>
            <input
              type="checkbox"
              checked={properties.checked || false}
              className="mr-2"
              disabled
            />
            {properties.label || field_name}
          </label>
        );

      case 'radio':
        return (
          <div className={`${baseClasses} px-3 py-2`}>
            {(properties.options || ['Option 1', 'Option 2']).map((option, index) => (
              <label key={index} className="flex items-center text-sm mb-1 cursor-pointer">
                <input
                  type="radio"
                  name={`radio-${component.id}`}
                  value={option}
                  className="mr-2"
                  disabled
                />
                {option}
              </label>
            ))}
          </div>
        );

      case 'button':
        return (
          <button
            className={`${baseClasses} px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50`}
            disabled
          >
            {properties.text || field_name}
          </button>
        );

      case 'file':
        return (
          <div className={`${baseClasses} px-3 py-2 text-sm bg-gray-50`}>
            <div className="flex items-center">
              <span className="text-gray-500">Choose file...</span>
            </div>
          </div>
        );

      case 'image':
        return (
          <div className={`${baseClasses} flex items-center justify-center bg-gray-100 text-gray-500 text-sm`}>
            {properties.src ? (
              <img
                src={properties.src}
                alt={properties.alt || 'Image'}
                className="w-full h-full object-cover rounded"
              />
            ) : (
              <div className="text-center">
                <div className="text-2xl mb-1">🖼️</div>
                <div>Image</div>
              </div>
            )}
          </div>
        );

      case 'container':
        return (
          <div className={`${baseClasses} bg-gray-50 flex items-center justify-center text-gray-500 text-sm`}>
            <div className="text-center">
              <div className="text-2xl mb-1">📦</div>
              <div>Container</div>
            </div>
          </div>
        );

      default:
        return (
          <div className={`${baseClasses} flex items-center justify-center bg-gray-100 text-gray-500 text-sm`}>
            Unknown Component
          </div>
        );
    }
  };

  return (
    <div className="relative w-full h-full group">
      {renderComponent()}
      
      {/* Component Label */}
      {isSelected && (
        <div className="absolute -top-6 left-0 bg-blue-600 text-white text-xs px-2 py-1 rounded text-nowrap">
          {field_name}
        </div>
      )}
    </div>
  );
};

export default ComponentRenderer;
