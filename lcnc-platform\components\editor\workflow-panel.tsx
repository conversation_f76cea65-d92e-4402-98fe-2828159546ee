"use client"

import { useState, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

interface WorkflowNode {
  id: string
  type: "trigger" | "action" | "condition"
  title: string
  description: string
  connected: boolean
  position: { x: number; y: number }
}

interface WorkflowPanelProps {
  selectedElements: any[]
}

export function WorkflowPanel({ selectedElements }: WorkflowPanelProps) {
  const [workflowNodes, setWorkflowNodes] = useState<WorkflowNode[]>([
    {
      id: "1",
      type: "trigger",
      title: "Form Submit",
      description: "When user submits the form",
      connected: false,
      position: { x: 50, y: 50 },
    },
    {
      id: "2",
      type: "action",
      title: "Send Email",
      description: "Send notification email",
      connected: false,
      position: { x: 50, y: 150 },
    },
    {
      id: "3",
      type: "condition",
      title: "Validate Input",
      description: "Check if input is valid",
      connected: false,
      position: { x: 50, y: 250 },
    },
  ])

  const [draggedNode, setDraggedNode] = useState<string | null>(null)

  const handleNodeDragStart = useCallback((nodeId: string) => {
    setDraggedNode(nodeId)
  }, [])

  const handleNodeDragEnd = useCallback(() => {
    setDraggedNode(null)
  }, [])

  const connectNode = useCallback((nodeId: string) => {
    setWorkflowNodes((prev) =>
      prev.map((node) => (node.id === nodeId ? { ...node, connected: !node.connected } : node)),
    )
  }, [])

  const addWorkflowNode = useCallback(
    (type: "trigger" | "action" | "condition") => {
      const newNode: WorkflowNode = {
        id: Date.now().toString(),
        type,
        title: `New ${type}`,
        description: `Configure this ${type}`,
        connected: false,
        position: { x: 50, y: workflowNodes.length * 100 + 50 },
      }
      setWorkflowNodes((prev) => [...prev, newNode])
    },
    [workflowNodes.length],
  )

  return (
    <div className="w-80 bg-gray-900 border-r border-gray-700 flex flex-col">
      {/* Workflow Header */}
      <div className="p-4 border-b border-gray-700">
        <h3 className="text-white font-semibold mb-3">Workflow</h3>
        <div className="flex gap-2 mb-4">
          <Button size="sm" variant="outline" onClick={() => addWorkflowNode("trigger")} className="text-xs">
            + Trigger
          </Button>
          <Button size="sm" variant="outline" onClick={() => addWorkflowNode("action")} className="text-xs">
            + Action
          </Button>
          <Button size="sm" variant="outline" onClick={() => addWorkflowNode("condition")} className="text-xs">
            + Condition
          </Button>
        </div>
      </div>

      {/* Workflow Canvas */}
      <div className="flex-1 p-4 relative overflow-auto">
        <div className="relative h-full min-h-96 bg-gray-800 rounded-lg border border-gray-600">
          {workflowNodes.map((node, index) => (
            <div key={node.id}>
              {/* Connection Line */}
              {index > 0 && workflowNodes[index - 1].connected && node.connected && (
                <svg
                  className="absolute pointer-events-none"
                  style={{
                    left: 0,
                    top: 0,
                    width: "100%",
                    height: "100%",
                    zIndex: 1,
                  }}
                >
                  <line
                    x1={workflowNodes[index - 1].position.x + 60}
                    y1={workflowNodes[index - 1].position.y + 30}
                    x2={node.position.x + 60}
                    y2={node.position.y + 30}
                    stroke="#3b82f6"
                    strokeWidth="2"
                    markerEnd="url(#arrowhead)"
                  />
                  <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                      <polygon points="0 0, 10 3.5, 0 7" fill="#3b82f6" />
                    </marker>
                  </defs>
                </svg>
              )}

              {/* Workflow Node */}
              <Card
                className={`absolute w-32 cursor-move transition-all duration-200 ${
                  node.connected ? "border-blue-500 bg-blue-50" : "border-gray-600 bg-gray-700 hover:bg-gray-600"
                } ${draggedNode === node.id ? "scale-105 shadow-lg" : ""}`}
                style={{
                  left: node.position.x,
                  top: node.position.y,
                  zIndex: 2,
                }}
                draggable
                onDragStart={() => handleNodeDragStart(node.id)}
                onDragEnd={handleNodeDragEnd}
                onClick={() => connectNode(node.id)}
              >
                <CardHeader className="p-2">
                  <div className="flex items-center justify-between">
                    <Badge
                      variant={node.type === "trigger" ? "default" : node.type === "action" ? "secondary" : "outline"}
                      className="text-xs"
                    >
                      {node.type}
                    </Badge>
                    <div className={`w-2 h-2 rounded-full ${node.connected ? "bg-green-500" : "bg-gray-400"}`} />
                  </div>
                </CardHeader>
                <CardContent className="p-2 pt-0">
                  <div className="text-xs font-medium text-white mb-1">{node.title}</div>
                  <div className="text-xs text-gray-300">{node.description}</div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>

      {/* Selected Elements Info */}
      {selectedElements.length > 0 && (
        <div className="p-4 border-t border-gray-700">
          <h4 className="text-white font-medium mb-2">Selected Elements</h4>
          <div className="space-y-2">
            {selectedElements.map((element, index) => (
              <div key={index} className="text-sm text-gray-300 bg-gray-800 p-2 rounded">
                {element.type} - {element.properties?.content || element.type}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
