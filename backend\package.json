{"name": "rapidgeniai-backend", "version": "1.0.0", "description": "Backend for RapidGeniAI - Low-Code/No-Code App Builder Platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["lcnc", "low-code", "no-code", "app-builder", "express", "nodejs"], "author": "RapidGeniAI", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "uuid": "^9.0.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}