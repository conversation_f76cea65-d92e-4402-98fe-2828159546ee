import React, { useState } from 'react';
import { X } from 'lucide-react';
import LoadingSpinner from './LoadingSpinner';

const CreateAppModal = ({ isOpen, onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    
    // Clear error for this field
    if (errors[e.target.name]) {
      setErrors({
        ...errors,
        [e.target.name]: ''
      });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'App name is required';
    } else if (formData.name.length > 100) {
      newErrors.name = 'App name must be less than 100 characters';
    }

    if (formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    
    try {
      await onSubmit({
        name: formData.name.trim(),
        description: formData.description.trim()
      });
      
      // Reset form
      setFormData({ name: '', description: '' });
      setErrors({});
    } catch (error) {
      console.error('Error in form submission:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (!loading) {
      setFormData({ name: '', description: '' });
      setErrors({});
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-md flex items-center justify-center p-6 z-50 animate-fadeIn">
      <div className="relative max-w-xl w-full animate-slideIn">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-3xl blur-xl"></div>
        <div className="relative card shadow-3xl border-2 border-white/20 hover:border-white/30 transition-all duration-300">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                Create New App
              </h2>
              <p className="text-gray-600">Start building your next amazing application</p>
            </div>
            <button
              onClick={handleClose}
              disabled={loading}
              className="text-gray-400 hover:text-gray-600 transition-colors p-3 hover:bg-gray-100 rounded-xl"
            >
              <X size={24} />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit}>
            <div className="space-y-8">
              <div className="form-group">
                <label htmlFor="name" className="form-label text-gray-700 text-base font-semibold">
                  App Name *
                </label>
                <input
                  id="name"
                  name="name"
                  type="text"
                  required
                  className={`form-input text-base py-4 px-5 border-2 rounded-xl ${errors.name ? 'border-red-500' : 'border-gray-200 hover:border-blue-300 focus:border-blue-500'}`}
                  placeholder="Enter a descriptive name for your app"
                  value={formData.name}
                  onChange={handleChange}
                  disabled={loading}
                  maxLength={100}
                />
                {errors.name && (
                  <p className="text-red-500 text-sm mt-2 font-medium">{errors.name}</p>
                )}
              </div>

              <div className="form-group">
                <label htmlFor="description" className="form-label text-gray-700 text-base font-semibold">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  className={`form-textarea text-base py-4 px-5 border-2 rounded-xl min-h-[120px] ${errors.description ? 'border-red-500' : 'border-gray-200 hover:border-blue-300 focus:border-blue-500'}`}
                  placeholder="Describe what your app will do and its main features..."
                  value={formData.description}
                  onChange={handleChange}
                  disabled={loading}
                  maxLength={500}
                  rows={4}
                />
                <div className="flex justify-between items-center mt-2">
                  {errors.description && (
                    <p className="text-red-500 text-sm font-medium">{errors.description}</p>
                  )}
                  <p className="text-gray-500 text-sm ml-auto font-medium">
                    {formData.description.length}/500 characters
                  </p>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-4 mt-10 pt-6 border-t border-gray-100">
              <button
                type="button"
                onClick={handleClose}
                disabled={loading}
                className="btn btn-outline px-8 py-3 text-base font-semibold"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="btn btn-primary px-8 py-3 text-base font-semibold shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
              >
                {loading ? (
                  <>
                    <LoadingSpinner size="small" />
                    Creating...
                  </>
                ) : (
                  'Create App'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default CreateAppModal;
