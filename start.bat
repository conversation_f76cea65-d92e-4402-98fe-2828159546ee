@echo off
echo Starting RapidGeniAI Platform...
echo.

echo Installing dependencies...
call npm run install-all

echo.
echo Starting backend and frontend servers...
echo Backend will run on http://localhost:5000
echo Frontend will run on http://localhost:3000
echo.

start "RapidGeniAI Backend" cmd /k "cd backend && npm run dev"
timeout /t 3 /nobreak > nul
start "RapidGeniAI Frontend" cmd /k "cd frontend && npm start"

echo.
echo Both servers are starting...
echo Check the opened terminal windows for status.
echo.
pause
