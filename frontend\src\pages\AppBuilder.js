import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { appsAPI, componentsAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
import ComponentPalette from '../components/ComponentPalette';
import CanvasArea from '../components/CanvasArea';
import PropertiesPanel from '../components/PropertiesPanel';
import AppPreview from '../components/AppPreview';
import {
  ArrowLeft,
  Save,
  ZoomIn,
  ZoomOut,
  Play,
  FileText,
  Layers
} from 'lucide-react';
import toast from 'react-hot-toast';

const AppBuilder = () => {
  const { appId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const [app, setApp] = useState(null);
  const [components, setComponents] = useState([]);
  const [selectedComponent, setSelectedComponent] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [selectedTool, setSelectedTool] = useState('move');
  const [showPreview, setShowPreview] = useState(false);

  const fetchAppData = useCallback(async () => {
    try {
      setLoading(true);
      
      // Fetch app details and components in parallel
      const [appResponse, componentsResponse] = await Promise.all([
        appsAPI.getById(appId),
        componentsAPI.getByAppId(appId)
      ]);
      
      setApp(appResponse.data.app);
      setComponents(componentsResponse.data.components);
    } catch (error) {
      console.error('Error fetching app data:', error);
      toast.error('Failed to load app');
      navigate('/dashboard');
    } finally {
      setLoading(false);
    }
  }, [appId, navigate]);

  useEffect(() => {
    if (appId) {
      fetchAppData();
    }
  }, [appId, fetchAppData]);

  const handleAddComponent = async (componentData) => {
    try {
      const response = await componentsAPI.create({
        ...componentData,
        appId
      });
      
      const newComponent = response.data.component;
      setComponents([...components, newComponent]);
      setSelectedComponent(newComponent);
      toast.success('Component added successfully');
    } catch (error) {
      console.error('Error adding component:', error);
      toast.error('Failed to add component');
    }
  };

  const handleUpdateComponent = async (componentId, updates) => {
    try {
      const response = await componentsAPI.update(componentId, updates);
      const updatedComponent = response.data.component;
      
      setComponents(components.map(comp => 
        comp.id === componentId ? updatedComponent : comp
      ));
      
      if (selectedComponent?.id === componentId) {
        setSelectedComponent(updatedComponent);
      }
      
      toast.success('Component updated');
    } catch (error) {
      console.error('Error updating component:', error);
      toast.error('Failed to update component');
    }
  };

  const handleDeleteComponent = async (componentId) => {
    try {
      await componentsAPI.delete(componentId);
      setComponents(components.filter(comp => comp.id !== componentId));
      
      if (selectedComponent?.id === componentId) {
        setSelectedComponent(null);
      }
      
      toast.success('Component deleted');
    } catch (error) {
      console.error('Error deleting component:', error);
      toast.error('Failed to delete component');
    }
  };

  const handleSaveApp = async () => {
    try {
      setSaving(true);
      await appsAPI.update(appId, {
        updatedAt: new Date().toISOString()
      });
      toast.success('App saved successfully');
    } catch (error) {
      console.error('Error saving app:', error);
      toast.error('Failed to save app');
    } finally {
      setSaving(false);
    }
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 25, 25));
  };

  const handleToolSelect = (tool) => {
    setSelectedTool(tool);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  if (!app) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">App not found</h2>
          <button
            onClick={() => navigate('/dashboard')}
            className="btn btn-primary"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-white">
      {/* Figma-style Header */}
      <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center justify-between">
        {/* Left section */}
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
              <span className="text-white font-bold text-sm">RG</span>
            </div>
            <span className="text-white font-medium">{app.name}</span>
          </div>

          <button
            onClick={() => navigate('/dashboard')}
            className="text-gray-400 hover:text-white transition-colors p-1"
          >
            <ArrowLeft size={16} />
          </button>
        </div>

        {/* Center section - Figma-style toolbar */}
        <div className="flex items-center space-x-1 bg-gray-700 rounded-lg p-1">
          <button
            onClick={() => handleToolSelect('move')}
            className={`p-2 rounded transition-colors ${
              selectedTool === 'move' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-600'
            }`}
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M8 2L6 4h4L8 2zM2 8l2-2v4L2 8zm12 0l-2-2v4l2-2zM8 14l2-2H6l2 2z"/>
            </svg>
          </button>
          <button
            onClick={() => handleToolSelect('frame')}
            className={`p-2 rounded transition-colors ${
              selectedTool === 'frame' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-600'
            }`}
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M2 2h12v12H2V2zm1 1v10h10V3H3z"/>
            </svg>
          </button>
          <button
            onClick={() => handleToolSelect('circle')}
            className={`p-2 rounded transition-colors ${
              selectedTool === 'circle' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-600'
            }`}
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <circle cx="8" cy="8" r="6"/>
            </svg>
          </button>
          <button
            onClick={() => handleToolSelect('text')}
            className={`p-2 rounded transition-colors ${
              selectedTool === 'text' ? 'bg-blue-600 text-white' : 'text-gray-300 hover:text-white hover:bg-gray-600'
            }`}
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
              <path d="M4 2h8v2H9v10H7V4H4V2z"/>
            </svg>
          </button>
        </div>

        {/* Right section */}
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2 bg-gray-700 rounded-lg p-1">
            <button
              onClick={handleZoomOut}
              className="p-1 text-gray-300 hover:text-white hover:bg-gray-600 rounded transition-colors"
            >
              <ZoomOut size={14} />
            </button>
            <span className="text-sm text-gray-300 px-2 min-w-[50px] text-center">{zoomLevel}%</span>
            <button
              onClick={handleZoomIn}
              className="p-1 text-gray-300 hover:text-white hover:bg-gray-600 rounded transition-colors"
            >
              <ZoomIn size={14} />
            </button>
          </div>
          <button
            onClick={handleSaveApp}
            disabled={saving}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors disabled:opacity-50 flex items-center space-x-2"
          >
            <Save size={16} />
            <span>{saving ? 'Saving...' : 'Save'}</span>
          </button>
          <button
            onClick={() => setShowPreview(true)}
            className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
          >
            <Play size={16} />
            <span>Preview</span>
          </button>
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <span className="text-xs text-white font-medium">{user?.username?.charAt(0).toUpperCase()}</span>
          </div>
        </div>
      </div>

      {/* Main Content - Figma Layout */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Figma style */}
        <div className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col">
          {/* Sidebar Header */}
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-6 h-6 bg-gray-600 rounded flex items-center justify-center">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="white">
                  <path d="M2 2h8v8H2V2z"/>
                </svg>
              </div>
              <span className="text-white text-sm font-medium">Pages</span>
            </div>
            <div className="space-y-1">
              <div className="flex items-center space-x-2 p-2 bg-gray-700 rounded text-white text-sm">
                <span>Page 1</span>
              </div>
            </div>
          </div>

          {/* Layers Section */}
          <div className="flex-1 p-4">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-6 h-6 bg-gray-600 rounded flex items-center justify-center">
                <svg width="12" height="12" viewBox="0 0 12 12" fill="white">
                  <path d="M2 2h8v2H2V2zm0 3h8v2H2V5zm0 3h8v2H2V8z"/>
                </svg>
              </div>
              <span className="text-white text-sm font-medium">Layers</span>
            </div>
            <div className="space-y-1 text-sm text-gray-300">
              {components.map((component) => (
                <div
                  key={component.id}
                  className={`flex items-center space-x-2 p-2 rounded cursor-pointer hover:bg-gray-700 ${
                    selectedComponent?.id === component.id ? 'bg-blue-600 text-white' : ''
                  }`}
                  onClick={() => setSelectedComponent(component)}
                >
                  <div className="w-4 h-4 bg-gray-600 rounded-sm flex items-center justify-center">
                    <span className="text-xs">{component.field_type.charAt(0).toUpperCase()}</span>
                  </div>
                  <span className="truncate">{component.field_name}</span>
                </div>
              ))}
              {components.length === 0 && (
                <div className="text-gray-500 text-xs py-4 text-center">
                  No components yet
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Center Canvas Area - Modern Design */}
        <div className="flex-1 bg-[#0d1117] relative overflow-hidden">
          {/* Canvas Toolbar */}
          <div className="absolute top-6 left-1/2 transform -translate-x-1/2 z-10">
            <div className="bg-[#161b22] border border-[#30363d] rounded-xl shadow-2xl px-6 py-3 flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-white font-medium">Canvas</span>
              </div>
              <div className="text-gray-400 text-sm">
                {components.length} component{components.length !== 1 ? 's' : ''}
              </div>
              <div className="text-gray-400 text-sm">
                {zoomLevel}% zoom
              </div>
            </div>
          </div>

          {/* Canvas */}
          <div className="h-full p-8 overflow-auto">
            <div className="max-w-6xl mx-auto">
              <div className="bg-[#161b22] border border-[#30363d] rounded-2xl shadow-2xl overflow-hidden">
                <CanvasArea
                  components={components}
                  selectedComponent={selectedComponent}
                  onSelectComponent={setSelectedComponent}
                  onUpdateComponent={handleUpdateComponent}
                  onDeleteComponent={handleDeleteComponent}
                  zoomLevel={zoomLevel}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Right Sidebar - Properties Panel */}
        <div className="w-96 bg-[#161b22] border-l border-[#30363d] overflow-y-auto shadow-xl">
          <div className="p-6">
            <div className="flex items-center space-x-3 mb-8">
              <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                  <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
              </div>
              <div>
                <h3 className="text-white font-semibold text-lg">Design</h3>
                <p className="text-gray-400 text-sm">Customize your components</p>
              </div>
            </div>

            {/* Properties Panel */}
            <PropertiesPanel
              selectedComponent={selectedComponent}
              onUpdateComponent={handleUpdateComponent}
            />
          </div>
        </div>
      </div>

      {/* App Preview Modal */}
      {showPreview && (
        <AppPreview
          app={app}
          components={components}
          onClose={() => setShowPreview(false)}
        />
      )}
    </div>
  );
};

export default AppBuilder;
