"use client"

import type React from "react"
import { useRef, useCallback, useEffect, useState } from "react"
import { useCanvasState, type CanvasElement } from "./canvas-state-context"

interface EditorCanvasProps {
  canvasSize: { width: number; height: number }
  selectedElement: any
  onSelectElement: (element: any) => void
}

export function EditorCanvas({ canvasSize, selectedElement, onSelectElement }: EditorCanvasProps) {
  const canvasRef = useRef<HTMLDivElement>(null)
  const { state, dispatch, selectElement, clearSelection, addElement } = useCanvasState()
  const [alignmentGuides, setAlignmentGuides] = useState<Array<{ x?: number; y?: number }>>([]) // Added alignment guides state
  const [dragPreview, setDragPreview] = useState<{ x: number; y: number; width: number; height: number } | null>(null) // Added drag preview
  const [resizeState, setResizeState] = useState<{
    isResizing: boolean
    elementId: string | null
    handle: string | null
    startPosition: { x: number; y: number }
    startSize: { width: number; height: number }
    startElementPosition: { x: number; y: number }
  }>({
    isResizing: false,
    elementId: null,
    handle: null,
    startPosition: { x: 0, y: 0 },
    startSize: { width: 0, height: 0 },
    startElementPosition: { x: 0, y: 0 },
  })
  const [comments, setComments] = useState<Array<{ id: string; x: number; y: number; text: string; author: string }>>(
    [],
  )
  const [showCommentInput, setShowCommentInput] = useState<{ x: number; y: number } | null>(null)

  const handleCanvasClick = useCallback(
    (e: React.MouseEvent) => {
      if (e.target === e.currentTarget) {
        clearSelection()
        onSelectElement(null)
      }
    },
    [clearSelection, onSelectElement],
  )

  const handleElementClick = useCallback(
    (element: CanvasElement, e: React.MouseEvent) => {
      e.stopPropagation()
      selectElement(element.id, e.ctrlKey || e.metaKey)
      onSelectElement(element)
    },
    [selectElement, onSelectElement],
  )

  const handleElementMouseDown = useCallback(
    (element: CanvasElement, e: React.MouseEvent) => {
      if (e.button !== 0) return

      const rect = canvasRef.current?.getBoundingClientRect()
      if (!rect) return

      const startPosition = { x: e.clientX, y: e.clientY }
      const offset = {
        x: e.clientX - rect.left - element.x,
        y: e.clientY - rect.top - element.y,
      }

      dispatch({
        type: "START_DRAG",
        elementId: element.id,
        startPosition,
        offset,
      })

      // Show drag preview
      setDragPreview({
        x: element.x,
        y: element.y,
        width: element.width,
        height: element.height,
      })
    },
    [dispatch],
  )

  const handleResizeMouseDown = useCallback((element: CanvasElement, handle: string, e: React.MouseEvent) => {
    e.stopPropagation()

    setResizeState({
      isResizing: true,
      elementId: element.id,
      handle,
      startPosition: { x: e.clientX, y: e.clientY },
      startSize: { width: element.width, height: element.height },
      startElementPosition: { x: element.x, y: element.y },
    })
  }, [])

  const handleMouseMove = useCallback(
    (e: MouseEvent) => {
      if (state.dragState.isDragging) {
        const rect = canvasRef.current?.getBoundingClientRect()
        if (!rect) return

        const newX = e.clientX - rect.left - state.dragState.offset.x
        const newY = e.clientY - rect.top - state.dragState.offset.y

        // Calculate alignment guides
        const guides: Array<{ x?: number; y?: number }> = []
        const threshold = 5

        state.elements.forEach((el) => {
          if (el.id === state.dragState.elementId) return

          // Vertical alignment guides
          if (Math.abs(el.x - newX) < threshold) guides.push({ x: el.x })
          if (Math.abs(el.x + el.width - newX) < threshold) guides.push({ x: el.x + el.width })
          if (Math.abs(el.x - (newX + (dragPreview?.width || 0))) < threshold) guides.push({ x: el.x })

          // Horizontal alignment guides
          if (Math.abs(el.y - newY) < threshold) guides.push({ y: el.y })
          if (Math.abs(el.y + el.height - newY) < threshold) guides.push({ y: el.y + el.height })
          if (Math.abs(el.y - (newY + (dragPreview?.height || 0))) < threshold) guides.push({ y: el.y })
        })

        setAlignmentGuides(guides)

        dispatch({
          type: "UPDATE_DRAG",
          position: { x: e.clientX - rect.left, y: e.clientY - rect.top },
        })

        // Update drag preview
        if (dragPreview) {
          setDragPreview((prev) => (prev ? { ...prev, x: newX, y: newY } : null))
        }
      } else if (resizeState.isResizing && resizeState.elementId) {
        const deltaX = e.clientX - resizeState.startPosition.x
        const deltaY = e.clientY - resizeState.startPosition.y

        let newWidth = resizeState.startSize.width
        let newHeight = resizeState.startSize.height
        let newX = resizeState.startElementPosition.x
        let newY = resizeState.startElementPosition.y

        switch (resizeState.handle) {
          case "se": // Southeast
            newWidth = Math.max(20, resizeState.startSize.width + deltaX)
            newHeight = Math.max(20, resizeState.startSize.height + deltaY)
            break
          case "sw": // Southwest
            newWidth = Math.max(20, resizeState.startSize.width - deltaX)
            newHeight = Math.max(20, resizeState.startSize.height + deltaY)
            newX = resizeState.startElementPosition.x + deltaX
            break
          case "ne": // Northeast
            newWidth = Math.max(20, resizeState.startSize.width + deltaX)
            newHeight = Math.max(20, resizeState.startSize.height - deltaY)
            newY = resizeState.startElementPosition.y + deltaY
            break
          case "nw": // Northwest
            newWidth = Math.max(20, resizeState.startSize.width - deltaX)
            newHeight = Math.max(20, resizeState.startSize.height - deltaY)
            newX = resizeState.startElementPosition.x + deltaX
            newY = resizeState.startElementPosition.y + deltaY
            break
          case "e": // East
            newWidth = Math.max(20, resizeState.startSize.width + deltaX)
            break
          case "w": // West
            newWidth = Math.max(20, resizeState.startSize.width - deltaX)
            newX = resizeState.startElementPosition.x + deltaX
            break
          case "s": // South
            newHeight = Math.max(20, resizeState.startSize.height + deltaY)
            break
          case "n": // North
            newHeight = Math.max(20, resizeState.startSize.height - deltaY)
            newY = resizeState.startElementPosition.y + deltaY
            break
        }

        dispatch({
          type: "UPDATE_ELEMENT",
          id: resizeState.elementId,
          updates: { width: newWidth, height: newHeight, x: newX, y: newY },
        })
      }
    },
    [
      state.dragState.isDragging,
      state.dragState.offset,
      state.dragState.elementId,
      state.elements,
      dragPreview,
      dispatch,
      resizeState,
    ],
  )

  const handleMouseUp = useCallback(() => {
    if (state.dragState.isDragging) {
      dispatch({ type: "END_DRAG" })
      setAlignmentGuides([])
      setDragPreview(null)
    } else if (resizeState.isResizing) {
      setResizeState({
        isResizing: false,
        elementId: null,
        handle: null,
        startPosition: { x: 0, y: 0 },
        startSize: { width: 0, height: 0 },
        startElementPosition: { x: 0, y: 0 },
      })
    }
  }, [state.dragState.isDragging, dispatch, resizeState.isResizing])

  const handleCanvasDoubleClick = useCallback((e: React.MouseEvent) => {
    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    setShowCommentInput({ x, y })
  }, [])

  const handleCommentSubmit = useCallback(
    (text: string) => {
      if (showCommentInput && text.trim()) {
        const newComment = {
          id: Date.now().toString(),
          x: showCommentInput.x,
          y: showCommentInput.y,
          text: text.trim(),
          author: "Current User",
        }
        setComments((prev) => [...prev, newComment])
      }
      setShowCommentInput(null)
    },
    [showCommentInput],
  )

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) return

      if ((e.ctrlKey || e.metaKey) && e.key === "z" && !e.shiftKey) {
        e.preventDefault()
        dispatch({ type: "UNDO" })
      } else if ((e.ctrlKey || e.metaKey) && (e.key === "y" || (e.key === "z" && e.shiftKey))) {
        e.preventDefault()
        dispatch({ type: "REDO" })
      } else if ((e.ctrlKey || e.metaKey) && e.key === "c") {
        e.preventDefault()
        dispatch({ type: "COPY_ELEMENTS", ids: state.selectedElementIds })
      } else if ((e.ctrlKey || e.metaKey) && e.key === "v") {
        e.preventDefault()
        dispatch({ type: "PASTE_ELEMENTS" })
      } else if ((e.ctrlKey || e.metaKey) && e.key === "d") {
        e.preventDefault()
        state.selectedElementIds.forEach((id) => {
          dispatch({ type: "DUPLICATE_ELEMENT", id })
        })
      } else if (e.key === "Delete" || e.key === "Backspace") {
        e.preventDefault()
        if (state.selectedElementIds.length > 0) {
          dispatch({ type: "DELETE_ELEMENTS", ids: state.selectedElementIds })
        }
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [dispatch, state.selectedElementIds])

  // Handle mouse events for dragging
  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove)
    document.addEventListener("mouseup", handleMouseUp)

    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
    }
  }, [handleMouseMove, handleMouseUp])

  const renderElement = (element: CanvasElement) => {
    const isSelected = state.selectedElementIds.includes(element.id)

    return (
      <div
        key={element.id}
        className={`absolute cursor-move select-none ${
          isSelected ? "ring-2 ring-blue-500 ring-offset-1" : "hover:ring-1 hover:ring-blue-300"
        }`}
        style={{
          left: element.x,
          top: element.y,
          width: element.width,
          height: element.height,
          transform: `rotate(${element.rotation}deg)`,
          opacity: element.opacity,
          zIndex: element.zIndex,
          visibility: element.visible ? "visible" : "hidden",
        }}
        onClick={(e) => handleElementClick(element, e)}
        onMouseDown={(e) => handleElementMouseDown(element, e)}
      >
        {element.type === "text" && (
          <div
            className="w-full h-full flex items-center"
            style={{
              fontSize: element.properties.fontSize,
              fontFamily: element.properties.fontFamily,
              fontWeight: element.properties.fontWeight,
              color: element.properties.color,
              textAlign: element.properties.textAlign,
              padding: `${element.properties.paddingTop || 0}px ${element.properties.paddingRight || 0}px ${element.properties.paddingBottom || 0}px ${element.properties.paddingLeft || 0}px`,
            }}
          >
            {element.properties.content || "Text Element"}
          </div>
        )}

        {element.type === "button" && (
          <button
            className="w-full h-full rounded"
            style={{
              backgroundColor: element.properties.backgroundColor || "#3b82f6",
              color: element.properties.color || "#ffffff",
              fontSize: element.properties.fontSize || "14px",
              fontFamily: element.properties.fontFamily || "Inter",
              fontWeight: element.properties.fontWeight || "500",
              border: `${element.properties.borderWidth || 0}px solid ${element.properties.borderColor || "transparent"}`,
              borderRadius: element.properties.borderRadius || "6px",
            }}
          >
            {element.properties.content || "Button"}
          </button>
        )}

        {element.type === "link" && (
          <a
            href={element.properties.href || "#"}
            className="w-full h-full flex items-center"
            style={{
              color: element.properties.color || "#3b82f6",
              fontSize: element.properties.fontSize || "14px",
              fontFamily: element.properties.fontFamily || "Inter",
              textDecoration: element.properties.underline ? "underline" : "none",
            }}
          >
            {element.properties.content || "Link"}
          </a>
        )}

        {element.type === "video" && (
          <video
            className="w-full h-full"
            controls
            style={{
              borderRadius: element.properties.borderRadius || "0px",
            }}
          >
            <source src={element.properties.src || "/placeholder-video.mp4"} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        )}

        {element.type === "html" && (
          <div
            className="w-full h-full border border-gray-300 bg-white p-2 text-xs font-mono"
            style={{
              borderRadius: element.properties.borderRadius || "4px",
            }}
          >
            <div className="text-gray-500 mb-1">HTML Element</div>
            <div dangerouslySetInnerHTML={{ __html: element.properties.content || "<p>HTML content here</p>" }} />
          </div>
        )}

        {element.type === "map" && (
          <div
            className="w-full h-full bg-green-100 border border-green-300 flex items-center justify-center"
            style={{
              borderRadius: element.properties.borderRadius || "4px",
            }}
          >
            <div className="text-center text-green-700">
              <div className="text-lg mb-1">🗺️</div>
              <div className="text-xs">Map Component</div>
              <div className="text-xs text-gray-500">{element.properties.location || "Location not set"}</div>
            </div>
          </div>
        )}

        {element.type === "alert" && (
          <div
            className={`w-full h-full p-3 rounded border-l-4 ${
              element.properties.alertType === "error"
                ? "bg-red-50 border-red-400 text-red-700"
                : element.properties.alertType === "warning"
                  ? "bg-yellow-50 border-yellow-400 text-yellow-700"
                  : element.properties.alertType === "success"
                    ? "bg-green-50 border-green-400 text-green-700"
                    : "bg-blue-50 border-blue-400 text-blue-700"
            }`}
          >
            <div className="font-medium text-sm">{element.properties.title || "Alert Title"}</div>
            <div className="text-xs mt-1">{element.properties.content || "Alert message content"}</div>
          </div>
        )}

        {element.type === "input" && (
          <input
            type={element.properties.inputType || "text"}
            placeholder={element.properties.placeholder || "Enter text..."}
            className="w-full h-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
            disabled={element.properties.disabled}
            required={element.properties.required}
            style={{
              fontSize: element.properties.fontSize || "16px",
              fontFamily: element.properties.fontFamily || "Inter",
            }}
          />
        )}

        {element.type === "checkbox" && (
          <label className="w-full h-full flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              className="w-5 h-5 rounded-md border-2 border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-2 transition-all duration-200"
              disabled={element.properties.disabled}
              required={element.properties.required}
            />
            <span className="text-gray-700 font-medium" style={{ fontSize: element.properties.fontSize || "16px" }}>
              {element.properties.label || "Checkbox Label"}
            </span>
          </label>
        )}

        {element.type === "dropdown" && (
          <select
            className="w-full h-full px-4 py-3 border-2 border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm appearance-none"
            disabled={element.properties.disabled}
            required={element.properties.required}
            style={{
              fontSize: element.properties.fontSize || "16px",
              fontFamily: element.properties.fontFamily || "Inter",
              backgroundImage:
                'url(\'data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>\')',
              backgroundRepeat: "no-repeat",
              backgroundPosition: "right 12px center",
              backgroundSize: "12px",
            }}
          >
            <option value="">{element.properties.placeholder || "Select option..."}</option>
            {(element.properties.options || ["Option 1", "Option 2", "Option 3"]).map(
              (option: string, index: number) => (
                <option key={index} value={option}>
                  {option}
                </option>
              ),
            )}
          </select>
        )}

        {element.type === "searchbox" && (
          <div className="w-full h-full relative">
            <input
              type="search"
              placeholder={element.properties.placeholder || "Search..."}
              className="w-full h-full pl-10 pr-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={element.properties.disabled}
              style={{
                fontSize: element.properties.fontSize || "14px",
                fontFamily: element.properties.fontFamily || "Inter",
              }}
            />
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">🔍</div>
          </div>
        )}

        {element.type === "radio" && (
          <div className="w-full h-full flex flex-col space-y-2">
            {(element.properties.options || ["Option 1", "Option 2"]).map((option: string, index: number) => (
              <label key={index} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="radio"
                  name={`radio-${element.id}`}
                  value={option}
                  className="text-blue-600 focus:ring-blue-500"
                  disabled={element.properties.disabled}
                />
                <span className="text-sm" style={{ fontSize: element.properties.fontSize || "14px" }}>
                  {option}
                </span>
              </label>
            ))}
          </div>
        )}

        {element.type === "datepicker" && (
          <input
            type="date"
            className="w-full h-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={element.properties.disabled}
            required={element.properties.required}
            style={{
              fontSize: element.properties.fontSize || "14px",
              fontFamily: element.properties.fontFamily || "Inter",
            }}
          />
        )}

        {element.type === "imageuploader" && (
          <div className="w-full h-full border-2 border-dashed border-gray-300 rounded flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 cursor-pointer">
            <div className="text-2xl mb-2">📁</div>
            <div className="text-sm text-gray-600">Upload Image</div>
            <div className="text-xs text-gray-400 mt-1">Click or drag to upload</div>
          </div>
        )}

        {element.type === "fileuploader" && (
          <div className="w-full h-full border-2 border-dashed border-gray-300 rounded flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 cursor-pointer">
            <div className="text-2xl mb-2">📎</div>
            <div className="text-sm text-gray-600">Upload File</div>
            <div className="text-xs text-gray-400 mt-1">Click or drag to upload</div>
          </div>
        )}

        {element.type === "rectangle" && (
          <div
            className="w-full h-full"
            style={{
              backgroundColor: element.properties.backgroundColor || "#3b82f6",
              borderColor: element.properties.borderColor || "transparent",
              borderWidth: element.properties.borderWidth || 0,
              borderStyle: "solid",
              borderRadius: element.properties.borderRadius || 0,
            }}
          />
        )}

        {element.type === "circle" && (
          <div
            className="w-full h-full rounded-full"
            style={{
              backgroundColor: element.properties.backgroundColor || "#3b82f6",
              borderColor: element.properties.borderColor || "transparent",
              borderWidth: element.properties.borderWidth || 0,
              borderStyle: "solid",
            }}
          />
        )}

        {element.type === "triangle" && (
          <div
            className="w-full h-full flex items-center justify-center"
            style={{
              backgroundColor: "transparent",
            }}
          >
            <div
              style={{
                width: 0,
                height: 0,
                borderLeft: `${element.width / 2}px solid transparent`,
                borderRight: `${element.width / 2}px solid transparent`,
                borderBottom: `${element.height}px solid ${element.properties.backgroundColor || "#3b82f6"}`,
              }}
            />
          </div>
        )}

        {element.type === "image" && (
          <img
            src={element.properties.src || "/placeholder.svg?height=100&width=100"}
            alt="Element"
            className="w-full h-full object-cover"
            style={{
              borderRadius: element.properties.borderRadius || 0,
            }}
          />
        )}

        {element.type === "container" && (
          <div
            className="w-full h-full border-2 border-dashed border-gray-300"
            style={{
              backgroundColor: element.properties.backgroundColor || "transparent",
              padding: element.properties.padding || 16,
              gap: element.properties.gap || 8,
              display: "flex",
              flexDirection: element.properties.flexDirection || "column",
              justifyContent: element.properties.justifyContent || "flex-start",
              alignItems: element.properties.alignItems || "stretch",
            }}
          >
            <div className="text-gray-500 text-sm">Container</div>
          </div>
        )}

        {isSelected && (
          <>
            <div
              className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded-full cursor-nw-resize border-2 border-white hover:bg-blue-600 transition-colors"
              onMouseDown={(e) => handleResizeMouseDown(element, "nw", e)}
            />
            <div
              className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full cursor-ne-resize border-2 border-white hover:bg-blue-600 transition-colors"
              onMouseDown={(e) => handleResizeMouseDown(element, "ne", e)}
            />
            <div
              className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded-full cursor-sw-resize border-2 border-white hover:bg-blue-600 transition-colors"
              onMouseDown={(e) => handleResizeMouseDown(element, "sw", e)}
            />
            <div
              className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded-full cursor-se-resize border-2 border-white hover:bg-blue-600 transition-colors"
              onMouseDown={(e) => handleResizeMouseDown(element, "se", e)}
            />

            <div
              className="absolute -top-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-blue-500 rounded-full cursor-n-resize border-2 border-white hover:bg-blue-600 transition-colors"
              onMouseDown={(e) => handleResizeMouseDown(element, "n", e)}
            />
            <div
              className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-blue-500 rounded-full cursor-s-resize border-2 border-white hover:bg-blue-600 transition-colors"
              onMouseDown={(e) => handleResizeMouseDown(element, "s", e)}
            />
            <div
              className="absolute -left-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 rounded-full cursor-w-resize border-2 border-white hover:bg-blue-600 transition-colors"
              onMouseDown={(e) => handleResizeMouseDown(element, "w", e)}
            />
            <div
              className="absolute -right-1 top-1/2 transform -translate-y-1/2 w-3 h-3 bg-blue-500 rounded-full cursor-e-resize border-2 border-white hover:bg-blue-600 transition-colors"
              onMouseDown={(e) => handleResizeMouseDown(element, "e", e)}
            />
          </>
        )}
      </div>
    )
  }

  return (
    <div className="flex-1 bg-gray-600 p-8 overflow-auto relative">
      <div className="flex justify-center">
        <div
          ref={canvasRef}
          className="bg-white shadow-2xl relative cursor-default"
          style={{
            width: Math.min(canvasSize.width, 1200),
            height: Math.min(canvasSize.height, 800),
            transform: canvasSize.width > 1200 ? `scale(${1200 / canvasSize.width})` : "none",
            transformOrigin: "top center",
          }}
          onClick={handleCanvasClick}
          onDoubleClick={handleCanvasDoubleClick}
        >
          {/* Canvas Content */}
          {state.elements.length === 0 && (
            <div className="absolute inset-0 flex items-center justify-center text-gray-400 pointer-events-none">
              <div className="text-center">
                <div className="text-lg mb-2">Start building your app</div>
                <div className="text-sm">Drag elements from the sidebar to get started</div>
              </div>
            </div>
          )}

          {/* Render Elements */}
          {state.elements.sort((a, b) => a.zIndex - b.zIndex).map((element) => renderElement(element))}

          {comments.map((comment) => (
            <div
              key={comment.id}
              className="absolute z-50 bg-yellow-100 border border-yellow-300 rounded-lg p-2 shadow-lg max-w-48"
              style={{ left: comment.x, top: comment.y }}
            >
              <div className="text-xs font-medium text-yellow-800">{comment.author}</div>
              <div className="text-sm text-yellow-700 mt-1">{comment.text}</div>
            </div>
          ))}

          {showCommentInput && (
            <div
              className="absolute z-50 bg-white border border-gray-300 rounded-lg p-3 shadow-lg"
              style={{ left: showCommentInput.x, top: showCommentInput.y }}
            >
              <input
                type="text"
                placeholder="Add a comment..."
                className="w-48 px-3 py-2 border border-gray-200 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleCommentSubmit(e.currentTarget.value)
                  } else if (e.key === "Escape") {
                    setShowCommentInput(null)
                  }
                }}
                onBlur={(e) => {
                  if (e.target.value.trim()) {
                    handleCommentSubmit(e.target.value)
                  } else {
                    setShowCommentInput(null)
                  }
                }}
              />
            </div>
          )}

          {alignmentGuides.map((guide, index) => (
            <div key={index}>
              {guide.x !== undefined && (
                <div
                  className="absolute top-0 bottom-0 w-px bg-blue-400 pointer-events-none z-50"
                  style={{ left: guide.x }}
                />
              )}
              {guide.y !== undefined && (
                <div
                  className="absolute left-0 right-0 h-px bg-blue-400 pointer-events-none z-50"
                  style={{ top: guide.y }}
                />
              )}
            </div>
          ))}

          {dragPreview && (
            <div
              className="absolute border-2 border-dashed border-blue-400 pointer-events-none z-40 bg-blue-100 bg-opacity-20"
              style={{
                left: dragPreview.x,
                top: dragPreview.y,
                width: dragPreview.width,
                height: dragPreview.height,
              }}
            />
          )}
        </div>
      </div>
    </div>
  )
}
