import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { GeistSans } from "geist/font/sans"
import { GeistMono } from "geist/font/mono"
import { AuthProvider } from "@/components/auth/auth-context"
import { ProjectProvider } from "@/components/projects/project-context"
import { CollaborationProvider } from "@/components/collaboration/collaboration-context"
import "./globals.css"

export const metadata: Metadata = {
  title: "LCNC Platform",
  description: "Low-Code/No-Code Platform for Building Applications",
  generator: "v0.dev",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <style>{`
html {
  font-family: ${GeistSans.style.fontFamily};
  --font-sans: ${GeistSans.variable};
  --font-mono: ${GeistMono.variable};
}
        `}</style>
      </head>
      <body>
        <AuthProvider>
          <ProjectProvider>
            <CollaborationProvider>{children}</CollaborationProvider>
          </ProjectProvider>
        </AuthProvider>
      </body>
    </html>
  )
}
