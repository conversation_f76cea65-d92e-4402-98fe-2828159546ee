"use client"

import type React from "react"
import { useState } from "react"
import { Input } from "@/components/ui/input"
import {
  Type,
  Square,
  Circle,
  ImageIcon,
  Link,
  AlertTriangle,
  Video,
  Code,
  Map,
  Search,
  CheckSquare,
  ChevronDown,
  RadioIcon,
  Calendar,
  Upload,
  FileText,
  Triangle,
  Hexagon,
  Star,
  Heart,
  Diamond,
  ChevronRight,
} from "lucide-react"
import { useCanvasState } from "./canvas-state-context"

export function EditorSidebar() {
  const { addElement } = useCanvasState()
  const [searchQuery, setSearchQuery] = useState("")
  const [expandedCategory, setExpandedCategory] = useState<string | null>(null)

  const shapeVariants = [
    { icon: Square, label: "Rectangle", id: "rectangle" },
    { icon: Circle, label: "Circle", id: "circle" },
    { icon: Triangle, label: "Triangle", id: "triangle" },
    { icon: Hexagon, label: "Hexagon", id: "hexagon" },
    { icon: Star, label: "Star", id: "star" },
    { icon: Heart, label: "Heart", id: "heart" },
    { icon: Diamond, label: "Diamond", id: "diamond" },
  ]

  const elementCategories = [
    {
      title: "Visual Elements",
      elements: [
        { icon: Type, label: "Text", id: "text" as const },
        { icon: Square, label: "Button", id: "button" as const },
        { icon: Link, label: "Link", id: "link" as const },
        { icon: ImageIcon, label: "Images", id: "image" as const },
        {
          icon: Circle,
          label: "Shapes",
          id: "shapes" as const,
          hasSubItems: true,
          subItems: shapeVariants,
        },
        { icon: AlertTriangle, label: "Alert", id: "alert" as const },
        { icon: Video, label: "Video", id: "video" as const },
        { icon: Code, label: "Html", id: "html" as const },
        { icon: Map, label: "Map", id: "map" as const },
      ],
    },
    {
      title: "Input Form",
      elements: [
        { icon: Type, label: "Input", id: "input" as const },
        { icon: CheckSquare, label: "Checkbox", id: "checkbox" as const },
        { icon: ChevronDown, label: "Dropdown", id: "dropdown" as const },
        { icon: Search, label: "Searchbox", id: "searchbox" as const },
        { icon: RadioIcon, label: "Radio Button", id: "radio" as const },
        { icon: Calendar, label: "Date/Time Picker", id: "datetime" as const },
        { icon: Upload, label: "Image Uploader", id: "imageUploader" as const },
        { icon: FileText, label: "File Uploader", id: "fileUploader" as const },
      ],
    },
  ]

  const filteredCategories = elementCategories
    .map((category) => ({
      ...category,
      elements: category.elements.filter((element) => element.label.toLowerCase().includes(searchQuery.toLowerCase())),
    }))
    .filter((category) => category.elements.length > 0)

  const handleAddElement = (type: string, subType?: string) => {
    const elementType = subType || type

    const baseProps = { x: 200, y: 200 }
    let elementProps = {}

    switch (elementType) {
      case "input":
        elementProps = {
          placeholder: "Enter text...",
          type: "text",
          required: false,
          disabled: false,
        }
        break
      case "checkbox":
        elementProps = {
          label: "Checkbox option",
          checked: false,
          required: false,
        }
        break
      case "dropdown":
        elementProps = {
          options: ["Option 1", "Option 2", "Option 3"],
          placeholder: "Select an option",
          multiple: false,
        }
        break
      case "radio":
        elementProps = {
          name: "radio-group",
          options: ["Option 1", "Option 2", "Option 3"],
          selected: "",
        }
        break
      case "datetime":
        elementProps = {
          type: "date",
          format: "YYYY-MM-DD",
          showTime: false,
        }
        break
      case "searchbox":
        elementProps = {
          placeholder: "Search...",
          showIcon: true,
        }
        break
      case "imageUploader":
        elementProps = {
          acceptedTypes: "image/*",
          maxSize: "5MB",
          multiple: false,
        }
        break
      case "fileUploader":
        elementProps = {
          acceptedTypes: "*",
          maxSize: "10MB",
          multiple: true,
        }
        break
      case "rectangle":
      case "circle":
      case "triangle":
      case "hexagon":
      case "star":
      case "heart":
      case "diamond":
        elementProps = {
          shapeType: elementType,
          fill: "#3B82F6",
          stroke: "#1E40AF",
          strokeWidth: 2,
        }
        break
      default:
        break
    }

    addElement(elementType as any, baseProps, elementProps)
  }

  const handleImageUpload = (file: File) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string
      addElement("image", { x: 200, y: 200 }, { src: imageUrl })
    }
    reader.readAsDataURL(file)
  }

  const handleDragStart = (e: React.DragEvent, elementType: string, subType?: string) => {
    e.dataTransfer.setData(
      "application/json",
      JSON.stringify({
        type: subType || elementType,
        subType: subType,
      }),
    )
    e.dataTransfer.effectAllowed = "copy"
  }

  const toggleCategory = (categoryTitle: string) => {
    setExpandedCategory(expandedCategory === categoryTitle ? null : categoryTitle)
  }

  return (
    <div className="w-64 bg-gray-900 border-r border-gray-700 flex flex-col">
      {/* Search */}
      <div className="p-4 border-b border-gray-700">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <Input
            placeholder="Search Elements"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 bg-gray-800 border-gray-600 text-white placeholder-gray-400"
          />
        </div>
      </div>

      {/* Element Categories */}
      <div className="flex-1 overflow-y-auto p-4">
        {filteredCategories.map((category) => (
          <div key={category.title} className="mb-6">
            <h3 className="text-white font-medium mb-3 text-sm">{category.title}</h3>
            <div className="space-y-1">
              {category.elements.map((element) => (
                <div key={element.id}>
                  <div
                    draggable={!element.hasSubItems}
                    onDragStart={(e) => !element.hasSubItems && handleDragStart(e, element.id)}
                    onClick={() => {
                      if (element.hasSubItems) {
                        toggleCategory(element.id)
                      } else {
                        handleAddElement(element.id)
                      }
                    }}
                    className="flex items-center gap-3 p-2 rounded-md text-gray-300 hover:bg-gray-800 hover:text-white cursor-pointer transition-colors"
                  >
                    <element.icon size={16} />
                    <span className="text-sm flex-1">{element.label}</span>
                    {element.hasSubItems && (
                      <ChevronRight
                        size={14}
                        className={`transition-transform ${expandedCategory === element.id ? "rotate-90" : ""}`}
                      />
                    )}
                  </div>

                  {element.hasSubItems && expandedCategory === element.id && (
                    <div className="ml-6 mt-1 space-y-1">
                      {element.subItems?.map((subItem) => (
                        <div
                          key={subItem.id}
                          draggable
                          onDragStart={(e) => handleDragStart(e, element.id, subItem.id)}
                          onClick={() => handleAddElement(element.id, subItem.id)}
                          className="flex items-center gap-3 p-2 rounded-md text-gray-400 hover:bg-gray-800 hover:text-white cursor-pointer transition-colors text-sm"
                        >
                          <subItem.icon size={14} />
                          <span>{subItem.label}</span>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Hidden file input for image uploads */}
      <input
        type="file"
        id="image-upload"
        accept="image/*"
        className="hidden"
        onChange={(e) => {
          const file = e.target.files?.[0]
          if (file) handleImageUpload(file)
        }}
      />
    </div>
  )
}
