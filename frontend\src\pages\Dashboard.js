import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { appsAPI } from '../services/api';
import LoadingSpinner from '../components/LoadingSpinner';
import CreateAppModal from '../components/CreateAppModal';
import AppCard from '../components/AppCard';
import { Plus, Folder, Search } from 'lucide-react';
import toast from 'react-hot-toast';

const Dashboard = () => {
  const { user } = useAuth();
  const [apps, setApps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

  useEffect(() => {
    fetchApps();
  }, []);

  const fetchApps = async () => {
    try {
      setLoading(true);
      const response = await appsAPI.getAll();
      setApps(response.data.apps);
    } catch (error) {
      console.error('Error fetching apps:', error);
      toast.error('Failed to load apps');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateApp = async (appData) => {
    try {
      const response = await appsAPI.create(appData);
      setApps([response.data.app, ...apps]);
      setIsCreateModalOpen(false);
      toast.success('App created successfully!');
    } catch (error) {
      console.error('Error creating app:', error);
      toast.error('Failed to create app');
    }
  };

  const handleDeleteApp = async (appId) => {
    if (!window.confirm('Are you sure you want to delete this app? This action cannot be undone.')) {
      return;
    }

    try {
      await appsAPI.delete(appId);
      setApps(apps.filter(app => app.id !== appId));
      toast.success('App deleted successfully');
    } catch (error) {
      console.error('Error deleting app:', error);
      toast.error('Failed to delete app');
    }
  };

  const filteredApps = apps.filter(app =>
    app.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    app.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="container mx-auto px-6 py-12">
        {/* Header Section */}
        <div className="mb-16 text-center animate-fadeIn">
          <div className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-3xl mb-8 shadow-2xl animate-bounce">
            <span className="text-white font-bold text-3xl">👋</span>
          </div>
          <h1 className="text-6xl md:text-7xl font-black bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent mb-6 leading-tight">
            Welcome back,<br />
            <span className="text-5xl md:text-6xl">{user?.firstName || user?.username}!</span>
          </h1>
          <p className="text-2xl text-white/90 font-light max-w-2xl mx-auto leading-relaxed">
            Transform your ideas into reality with our powerful low-code platform
          </p>
          <div className="mt-8 flex justify-center">
            <div className="h-1 w-32 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
          </div>
        </div>

        {/* Actions Bar */}
        <div className="mb-16 animate-slideIn" style={{ animationDelay: '0.2s' }}>
          <div className="flex flex-col lg:flex-row justify-between items-center gap-8">
            <div className="relative flex-1 max-w-2xl">
              <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                <Search className="h-6 w-6 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search your applications..."
                className="w-full pl-16 pr-6 py-5 text-lg bg-white/95 backdrop-blur-xl border-2 border-white/20 rounded-2xl shadow-2xl focus:border-blue-400 focus:ring-4 focus:ring-blue-400/20 transition-all duration-300 placeholder-gray-400"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <button
              onClick={() => setIsCreateModalOpen(true)}
              className="btn btn-primary text-lg px-8 py-5 shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300 whitespace-nowrap"
            >
              <Plus size={24} />
              Create New App
            </button>
          </div>
        </div>

        {/* Apps Grid */}
        <div className="mb-20">
          {filteredApps.length === 0 ? (
            <div className="text-center py-24 animate-fadeIn">
              <div className="relative inline-block mb-8">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full blur-xl opacity-30 animate-pulse"></div>
                <div className="relative inline-flex items-center justify-center w-32 h-32 bg-gradient-to-br from-blue-500/20 to-purple-500/20 backdrop-blur-xl rounded-full border border-white/20">
                  <Folder className="h-16 w-16 text-white/80" />
                </div>
              </div>
              <h3 className="text-4xl font-bold text-white mb-4">
                {searchTerm ? 'No apps found' : 'Ready to build something amazing?'}
              </h3>
              <p className="text-xl text-white/70 mb-12 max-w-md mx-auto leading-relaxed">
                {searchTerm
                  ? 'Try adjusting your search terms or create a new app'
                  : 'Start your journey by creating your first application with our intuitive drag-and-drop builder'
                }
              </p>
              {!searchTerm && (
                <button
                  onClick={() => setIsCreateModalOpen(true)}
                  className="btn btn-primary text-xl px-12 py-6 shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
                >
                  <Plus size={28} />
                  Create Your First App
                </button>
              )}
            </div>
          ) : (
            <>
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-3xl font-bold text-white">
                  Your Applications
                  <span className="ml-3 text-lg font-normal text-white/60">
                    ({filteredApps.length} {filteredApps.length === 1 ? 'app' : 'apps'})
                  </span>
                </h2>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8 animate-fadeIn">
                {filteredApps.map((app, index) => (
                  <div key={app.id} className="animate-slideIn" style={{ animationDelay: `${index * 0.1}s` }}>
                    <AppCard
                      app={app}
                      onDelete={handleDeleteApp}
                    />
                  </div>
                ))}
              </div>
            </>
          )}
        </div>

        {/* Stats Section */}
        <div className="animate-slideIn" style={{ animationDelay: '0.4s' }}>
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Your Dashboard Overview</h2>
            <p className="text-white/70 text-lg">Track your application development progress</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-3xl blur-xl opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
              <div className="relative card text-center group-hover:scale-105 transition-all duration-300 border-2 border-white/10 hover:border-blue-400/30">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-3xl mb-6 shadow-2xl group-hover:shadow-3xl transition-shadow">
                  <span className="text-white font-bold text-2xl">{apps.length}</span>
                </div>
                <h3 className="text-xl font-bold text-gray-700 mb-3">Total Apps</h3>
                <p className="text-4xl font-black bg-gradient-to-r from-blue-500 to-cyan-500 bg-clip-text text-transparent mb-2">
                  {apps.length}
                </p>
                <p className="text-gray-500 text-sm">Applications created</p>
              </div>
            </div>

            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-emerald-500 rounded-3xl blur-xl opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
              <div className="relative card text-center group-hover:scale-105 transition-all duration-300 border-2 border-white/10 hover:border-green-400/30">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-green-500 to-emerald-500 rounded-3xl mb-6 shadow-2xl group-hover:shadow-3xl transition-shadow">
                  <span className="text-white font-bold text-2xl">{apps.filter(app => app.status === 'published').length}</span>
                </div>
                <h3 className="text-xl font-bold text-gray-700 mb-3">Published</h3>
                <p className="text-4xl font-black bg-gradient-to-r from-green-500 to-emerald-500 bg-clip-text text-transparent mb-2">
                  {apps.filter(app => app.status === 'published').length}
                </p>
                <p className="text-gray-500 text-sm">Live applications</p>
              </div>
            </div>

            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-3xl blur-xl opacity-20 group-hover:opacity-30 transition-opacity duration-300"></div>
              <div className="relative card text-center group-hover:scale-105 transition-all duration-300 border-2 border-white/10 hover:border-yellow-400/30">
                <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-3xl mb-6 shadow-2xl group-hover:shadow-3xl transition-shadow">
                  <span className="text-white font-bold text-2xl">{apps.filter(app => app.status === 'draft').length}</span>
                </div>
                <h3 className="text-xl font-bold text-gray-700 mb-3">In Progress</h3>
                <p className="text-4xl font-black bg-gradient-to-r from-yellow-500 to-orange-500 bg-clip-text text-transparent mb-2">
                  {apps.filter(app => app.status === 'draft').length}
                </p>
                <p className="text-gray-500 text-sm">Work in progress</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Create App Modal */}
      <CreateAppModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateApp}
      />
    </div>
  );
};

export default Dashboard;
