import React from 'react';
import { useDrag } from 'react-dnd';
import { 
  Type, 
  ChevronDown, 
  CheckSquare, 
  Circle, 
  FileText, 
  Hash, 
  Mail, 
  Calendar, 
  Upload, 
  MousePointer, 
  Image, 
  Square 
} from 'lucide-react';

const COMPONENT_ICONS = {
  text: Type,
  dropdown: ChevronDown,
  checkbox: CheckSquare,
  radio: Circle,
  textarea: FileText,
  number: Hash,
  email: Mail,
  date: Calendar,
  file: Upload,
  button: MousePointer,
  image: Image,
  container: Square
};

const DraggableComponent = ({ type, label, icon: Icon, onAdd }) => {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'component',
    item: { componentType: type },
    end: (item, monitor) => {
      const dropResult = monitor.getDropResult();
      if (item && dropResult) {
        onAdd({
          field_type: type,
          field_name: `${label} ${Date.now()}`,
          position: dropResult.position || { x: 100, y: 100, width: 200, height: 40 }
        });
      }
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }));

  return (
    <div
      ref={drag}
      className={`flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-700 cursor-move transition-all duration-200 group ${
        isDragging ? 'opacity-50' : ''
      }`}
    >
      <div className="w-8 h-8 bg-gray-600 rounded flex items-center justify-center group-hover:bg-blue-500 transition-colors">
        <Icon size={14} className="text-gray-300 group-hover:text-white" />
      </div>
      <span className="text-sm text-gray-300 group-hover:text-white">{label}</span>
    </div>
  );
};

const ComponentPalette = ({ onAddComponent }) => {

  const componentCategories = [
    {
      title: 'Input Fields',
      components: [
        { type: 'text', label: 'Text Input' },
        { type: 'textarea', label: 'Text Area' },
        { type: 'number', label: 'Number Input' },
        { type: 'email', label: 'Email Input' },
        { type: 'date', label: 'Date Picker' },
        { type: 'file', label: 'File Upload' }
      ]
    },
    {
      title: 'Selection',
      components: [
        { type: 'dropdown', label: 'Dropdown' },
        { type: 'checkbox', label: 'Checkbox' },
        { type: 'radio', label: 'Radio Button' }
      ]
    },
    {
      title: 'Actions',
      components: [
        { type: 'button', label: 'Button' }
      ]
    },
    {
      title: 'Display',
      components: [
        { type: 'image', label: 'Image' },
        { type: 'container', label: 'Container' }
      ]
    }
  ];



  return (
    <div>
      <div className="mb-6">
        <h3 className="text-white text-sm font-medium mb-4">Components</h3>

        <div className="space-y-6">
          {componentCategories.map((category) => (
            <div key={category.title}>
              <h4 className="text-gray-400 text-xs font-medium mb-3 uppercase tracking-wider">
                {category.title}
              </h4>

              <div className="space-y-1">
                {category.components.map((component) => {
                  const Icon = COMPONENT_ICONS[component.type] || Square;
                  return (
                    <DraggableComponent
                      key={component.type}
                      type={component.type}
                      label={component.label}
                      icon={Icon}
                      onAdd={onAddComponent}
                    />
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="mt-8 p-3 bg-gray-700 rounded-lg">
        <h4 className="text-white text-xs font-medium mb-2">💡 Tip</h4>
        <p className="text-gray-400 text-xs leading-relaxed">
          Drag components to the canvas to add them to your design.
        </p>
      </div>
    </div>
  );
};

export default ComponentPalette;
