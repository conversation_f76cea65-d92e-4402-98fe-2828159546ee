# RapidGeniAI - Low-Code/No-Code App Builder Platform

A full-stack LCNC platform that allows users to build applications using drag-and-drop components without writing code.

## 🚀 Features

### Backend (Node.js + Express)
- **JWT Authentication**: Secure user registration and login
- **App Management**: Create, read, update, delete applications
- **Component System**: Manage drag-and-drop components with properties
- **File Storage**: JSON-based data persistence (no database required)
- **Security**: Rate limiting, CORS, input validation, password hashing

### Frontend (React.js)
- **Responsive UI**: Modern, mobile-friendly interface
- **Drag & Drop Builder**: Visual component placement using react-dnd
- **Real-time Editing**: Live component property updates
- **User Dashboard**: Manage and organize applications
- **Component Library**: 12+ pre-built component types

## 🛠️ Tech Stack

- **Backend**: Node.js, Express.js, JWT, bcryptjs
- **Frontend**: React.js, React Router, React DnD, Axios
- **Storage**: JSON files (no database required)
- **Styling**: Custom CSS with utility classes
- **Icons**: Lucide React

## 📦 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### 1. Clone and Setup Backend

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Start development server
npm run dev
```

Backend will run on `http://localhost:5000`

### 2. Setup Frontend

```bash
# Open new terminal and navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm start
```

Frontend will run on `http://localhost:3000`

### 3. Access the Application

1. Open `http://localhost:3000` in your browser
2. Register a new account or login
3. Create your first app
4. Start building with drag-and-drop components!

## 📁 Project Structure

```
MyLCNC/
├── backend/                 # Node.js + Express backend
│   ├── config/             # Configuration files
│   ├── data/               # JSON data storage
│   ├── middleware/         # Express middleware
│   ├── routes/             # API routes
│   ├── utils/              # Utility functions
│   ├── package.json
│   └── server.js           # Main server file
├── frontend/               # React.js frontend
│   ├── public/             # Static files
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── contexts/       # React contexts
│   │   ├── pages/          # Page components
│   │   ├── services/       # API services
│   │   └── App.js          # Main app component
│   └── package.json
└── README.md               # This file
```

## 🎨 Supported Components

### Input Fields
- **Text Input** - Single line text entry
- **Textarea** - Multi-line text entry
- **Number Input** - Numeric values
- **Email Input** - Email with validation
- **Date Picker** - Date selection
- **File Upload** - File selection

### Selection Components
- **Dropdown** - Single/multiple selection
- **Checkbox** - Boolean input
- **Radio Buttons** - Single selection from options

### Action Components
- **Button** - Clickable actions

### Display Components
- **Image** - Image display
- **Container** - Layout container

## 🔧 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/verify` - Token verification
- `GET /api/auth/profile` - User profile

### Applications
- `GET /api/apps` - Get user apps
- `POST /api/apps` - Create new app
- `GET /api/apps/:id` - Get specific app
- `PUT /api/apps/:id` - Update app
- `DELETE /api/apps/:id` - Delete app

### Components
- `GET /api/components/app/:appId` - Get app components
- `POST /api/components` - Create component
- `PUT /api/components/:id` - Update component
- `DELETE /api/components/:id` - Delete component
- `GET /api/components/types/available` - Get component types

## 🔒 Security Features

- Password hashing with bcrypt
- JWT token authentication
- Rate limiting (100 requests per 15 minutes)
- CORS protection
- Input validation and sanitization
- Helmet security headers

## 🎯 Usage Guide

### Creating Your First App

1. **Register/Login**: Create an account or sign in
2. **Dashboard**: Click "Create New App" button
3. **App Details**: Enter app name and description
4. **Builder**: Drag components from the left palette to the canvas
5. **Properties**: Select components to edit their properties
6. **Save**: Use the save button to persist changes

### Component Editing

1. **Add Components**: Drag from palette to canvas
2. **Select**: Click on any component to select it
3. **Properties**: Edit component properties in the right panel
4. **Position**: Adjust position and size in the layout section
5. **Delete**: Use the delete button or trash icon

## 🚀 Deployment Notes

This project is designed for local development and testing. For production deployment:

1. **Environment Variables**: Set proper JWT secrets and API URLs
2. **Database**: Consider migrating from JSON files to a proper database
3. **Security**: Review and enhance security measures
4. **Performance**: Add caching and optimization
5. **Monitoring**: Implement logging and error tracking

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the README files in backend/ and frontend/ directories
2. Review the API documentation
3. Check browser console for frontend errors
4. Check server logs for backend errors

## 🎉 Features Roadmap

- [ ] Component templates and themes
- [ ] App preview and publishing
- [ ] User collaboration features
- [ ] Advanced component properties
- [ ] Form validation and submission
- [ ] Database integration options
- [ ] Export to code functionality
