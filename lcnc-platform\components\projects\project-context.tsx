"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

interface Project {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  creatorId: string
  creatorName: string
  creatorEmail: string
}

interface ProjectContextType {
  projects: Project[]
  currentProject: Project | null
  createProject: (projectData: Omit<Project, "id" | "createdAt" | "updatedAt">) => Promise<Project>
  updateProject: (id: string, updates: Partial<Project>) => Promise<boolean>
  deleteProject: (id: string) => Promise<boolean>
  setCurrentProject: (project: Project | null) => void
  getProjectById: (id: string) => Project | undefined
}

const ProjectContext = createContext<ProjectContextType | undefined>(undefined)

export function ProjectProvider({ children }: { children: ReactNode }) {
  const [projects, setProjects] = useState<Project[]>([])
  const [currentProject, setCurrentProject] = useState<Project | null>(null)

  useEffect(() => {
    // Load projects from localStorage
    const savedProjects = localStorage.getItem("projects")
    if (savedProjects) {
      setProjects(JSON.parse(savedProjects))
    }
  }, [])

  const saveProjects = (updatedProjects: Project[]) => {
    setProjects(updatedProjects)
    localStorage.setItem("projects", JSON.stringify(updatedProjects))
  }

  const createProject = async (projectData: Omit<Project, "id" | "createdAt" | "updatedAt">): Promise<Project> => {
    const newProject: Project = {
      ...projectData,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    const updatedProjects = [...projects, newProject]
    saveProjects(updatedProjects)
    setCurrentProject(newProject)

    return newProject
  }

  const updateProject = async (id: string, updates: Partial<Project>): Promise<boolean> => {
    try {
      const updatedProjects = projects.map((project) =>
        project.id === id ? { ...project, ...updates, updatedAt: new Date().toISOString() } : project,
      )
      saveProjects(updatedProjects)

      if (currentProject?.id === id) {
        setCurrentProject({ ...currentProject, ...updates, updatedAt: new Date().toISOString() })
      }

      return true
    } catch (error) {
      console.error("Failed to update project:", error)
      return false
    }
  }

  const deleteProject = async (id: string): Promise<boolean> => {
    try {
      const updatedProjects = projects.filter((project) => project.id !== id)
      saveProjects(updatedProjects)

      if (currentProject?.id === id) {
        setCurrentProject(null)
      }

      return true
    } catch (error) {
      console.error("Failed to delete project:", error)
      return false
    }
  }

  const getProjectById = (id: string): Project | undefined => {
    return projects.find((project) => project.id === id)
  }

  return (
    <ProjectContext.Provider
      value={{
        projects,
        currentProject,
        createProject,
        updateProject,
        deleteProject,
        setCurrentProject,
        getProjectById,
      }}
    >
      {children}
    </ProjectContext.Provider>
  )
}

export function useProjects() {
  const context = useContext(ProjectContext)
  if (context === undefined) {
    throw new Error("useProjects must be used within a ProjectProvider")
  }
  return context
}
