import React from 'react';
import { useDrop } from 'react-dnd';
import ComponentRenderer from './ComponentRenderer';
import { Trash2 } from 'lucide-react';

const CanvasArea = ({
  components,
  selectedComponent,
  onSelectComponent,
  onUpdateComponent,
  onDeleteComponent,
  zoomLevel = 100
}) => {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: 'component',
    drop: (item, monitor) => {
      const offset = monitor.getClientOffset();
      const canvasRect = document.getElementById('canvas').getBoundingClientRect();
      
      return {
        position: {
          x: offset.x - canvasRect.left,
          y: offset.y - canvasRect.top,
          width: 200,
          height: 40
        }
      };
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }));

  const handleComponentClick = (component, event) => {
    event.stopPropagation();
    onSelectComponent(component);
  };

  const handleCanvasClick = () => {
    onSelectComponent(null);
  };

  const handleDeleteSelected = () => {
    if (selectedComponent) {
      onDeleteComponent(selectedComponent.id);
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Canvas */}
      <div
        id="canvas"
        ref={drop}
        className={`flex-1 relative bg-white rounded-lg shadow-lg border-2 ${
          isOver ? 'border-blue-400 bg-blue-50' : 'border-gray-200'
        } transition-all duration-200`}
        onClick={handleCanvasClick}
        style={{ minHeight: '600px', margin: '20px' }}
      >
        {/* Drop Zone Indicator */}
        {isOver && (
          <div className="absolute inset-4 border-2 border-dashed border-blue-400 bg-blue-50 bg-opacity-50 flex items-center justify-center rounded-lg">
            <div className="text-blue-600 text-lg font-medium bg-white px-4 py-2 rounded-lg shadow-lg">
              Drop component here
            </div>
          </div>
        )}

        {/* Components */}
        {components.map((component) => (
          <div
            key={component.id}
            className={`absolute cursor-pointer transition-all duration-200 ${
              selectedComponent?.id === component.id
                ? 'ring-2 ring-blue-500 shadow-lg'
                : 'hover:ring-2 hover:ring-blue-300'
            }`}
            style={{
              left: (component.position?.x || 0) + 20,
              top: (component.position?.y || 0) + 20,
              width: component.position?.width || 200,
              height: component.position?.height || 40,
            }}
            onClick={(e) => handleComponentClick(component, e)}
          >
            <ComponentRenderer
              component={component}
              isSelected={selectedComponent?.id === component.id}
              onUpdate={(updates) => onUpdateComponent(component.id, updates)}
            />

            {/* Selection handles */}
            {selectedComponent?.id === component.id && (
              <>
                <div className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded-full"></div>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"></div>
                <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded-full"></div>
                <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded-full"></div>

                {/* Delete button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteSelected();
                  }}
                  className="absolute -top-8 -right-2 bg-red-500 text-white p-1 rounded text-xs hover:bg-red-600 transition-colors"
                  title="Delete component"
                >
                  <Trash2 size={12} />
                </button>
              </>
            )}
          </div>
        ))}

        {/* Empty State */}
        {components.length === 0 && !isOver && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-gray-400">
              <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                  <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2 text-gray-600">Start designing</h3>
              <p className="text-sm text-gray-500">
                Drag components from the right panel to build your app
              </p>
            </div>
          </div>
        )}

        {/* Figma-style Grid Background */}
        <div
          className="absolute inset-0 pointer-events-none opacity-5"
          style={{
            backgroundImage: `
              linear-gradient(to right, #000 1px, transparent 1px),
              linear-gradient(to bottom, #000 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px',
            margin: '20px'
          }}
        />
      </div>
    </div>
  );
};

export default CanvasArea;
