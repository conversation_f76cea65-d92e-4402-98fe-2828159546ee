"use client"

import type React from "react"

import { createContext, useContext, useReducer, useCallback, type ReactNode } from "react"

export interface CanvasElement {
  id: string
  type: "text" | "rectangle" | "circle" | "image" | "container"
  x: number
  y: number
  width: number
  height: number
  rotation: number
  opacity: number
  zIndex: number
  locked: boolean
  visible: boolean
  properties: {
    // Text properties
    content?: string
    fontSize?: number
    fontFamily?: string
    fontWeight?: string
    color?: string
    textAlign?: "left" | "center" | "right"
    // Shape properties
    backgroundColor?: string
    borderColor?: string
    borderWidth?: number
    borderRadius?: number
    // Image properties
    src?: string
    alt?: string
    // Container properties
    padding?: number
    gap?: number
    flexDirection?: "row" | "column"
    justifyContent?: "flex-start" | "center" | "flex-end" | "space-between"
    alignItems?: "flex-start" | "center" | "flex-end"
  }
}

interface CanvasState {
  elements: CanvasElement[]
  selectedElementIds: string[]
  clipboard: CanvasElement[]
  viewport: {
    zoom: number
    panX: number
    panY: number
  }
  history: {
    past: CanvasElement[][]
    present: CanvasElement[]
    future: CanvasElement[][]
  }
  dragState: {
    isDragging: boolean
    draggedElementId: string | null
    startPosition: { x: number; y: number }
    offset: { x: number; y: number }
  }
}

type CanvasAction =
  | { type: "ADD_ELEMENT"; element: CanvasElement }
  | { type: "UPDATE_ELEMENT"; id: string; updates: Partial<CanvasElement> }
  | { type: "DELETE_ELEMENT"; id: string }
  | { type: "DELETE_ELEMENTS"; ids: string[] }
  | { type: "SELECT_ELEMENT"; id: string; multiSelect?: boolean }
  | { type: "SELECT_ELEMENTS"; ids: string[] }
  | { type: "CLEAR_SELECTION" }
  | { type: "MOVE_ELEMENT"; id: string; x: number; y: number }
  | { type: "RESIZE_ELEMENT"; id: string; width: number; height: number }
  | { type: "DUPLICATE_ELEMENT"; id: string }
  | { type: "COPY_ELEMENTS"; ids: string[] }
  | { type: "PASTE_ELEMENTS"; position?: { x: number; y: number } }
  | { type: "BRING_TO_FRONT"; id: string }
  | { type: "SEND_TO_BACK"; id: string }
  | { type: "SET_VIEWPORT"; viewport: Partial<CanvasState["viewport"]> }
  | { type: "START_DRAG"; elementId: string; startPosition: { x: number; y: number }; offset: { x: number; y: number } }
  | { type: "UPDATE_DRAG"; position: { x: number; y: number } }
  | { type: "END_DRAG" }
  | { type: "UNDO" }
  | { type: "REDO" }
  | { type: "SAVE_STATE" }

const initialState: CanvasState = {
  elements: [],
  selectedElementIds: [],
  clipboard: [],
  viewport: { zoom: 1, panX: 0, panY: 0 },
  history: { past: [], present: [], future: [] },
  dragState: {
    isDragging: false,
    draggedElementId: null,
    startPosition: { x: 0, y: 0 },
    offset: { x: 0, y: 0 },
  },
}

function canvasReducer(state: CanvasState, action: CanvasAction): CanvasState {
  switch (action.type) {
    case "ADD_ELEMENT": {
      const newElements = [...state.elements, action.element]
      return {
        ...state,
        elements: newElements,
        selectedElementIds: [action.element.id],
        history: {
          past: [...state.history.past, state.elements],
          present: newElements,
          future: [],
        },
      }
    }

    case "UPDATE_ELEMENT": {
      const newElements = state.elements.map((el) => (el.id === action.id ? { ...el, ...action.updates } : el))
      return {
        ...state,
        elements: newElements,
        history: {
          past: [...state.history.past, state.elements],
          present: newElements,
          future: [],
        },
      }
    }

    case "DELETE_ELEMENT": {
      const newElements = state.elements.filter((el) => el.id !== action.id)
      return {
        ...state,
        elements: newElements,
        selectedElementIds: state.selectedElementIds.filter((id) => id !== action.id),
        history: {
          past: [...state.history.past, state.elements],
          present: newElements,
          future: [],
        },
      }
    }

    case "DELETE_ELEMENTS": {
      const newElements = state.elements.filter((el) => !action.ids.includes(el.id))
      return {
        ...state,
        elements: newElements,
        selectedElementIds: state.selectedElementIds.filter((id) => !action.ids.includes(id)),
        history: {
          past: [...state.history.past, state.elements],
          present: newElements,
          future: [],
        },
      }
    }

    case "SELECT_ELEMENT": {
      if (action.multiSelect) {
        const newSelection = state.selectedElementIds.includes(action.id)
          ? state.selectedElementIds.filter((id) => id !== action.id)
          : [...state.selectedElementIds, action.id]
        return { ...state, selectedElementIds: newSelection }
      }
      return { ...state, selectedElementIds: [action.id] }
    }

    case "SELECT_ELEMENTS":
      return { ...state, selectedElementIds: action.ids }

    case "CLEAR_SELECTION":
      return { ...state, selectedElementIds: [] }

    case "MOVE_ELEMENT": {
      const newElements = state.elements.map((el) => (el.id === action.id ? { ...el, x: action.x, y: action.y } : el))
      return { ...state, elements: newElements }
    }

    case "RESIZE_ELEMENT": {
      const newElements = state.elements.map((el) =>
        el.id === action.id ? { ...el, width: action.width, height: action.height } : el,
      )
      return { ...state, elements: newElements }
    }

    case "DUPLICATE_ELEMENT": {
      const element = state.elements.find((el) => el.id === action.id)
      if (!element) return state

      const duplicatedElement: CanvasElement = {
        ...element,
        id: `${element.id}_copy_${Date.now()}`,
        x: element.x + 20,
        y: element.y + 20,
        zIndex: Math.max(...state.elements.map((el) => el.zIndex)) + 1,
      }

      const newElements = [...state.elements, duplicatedElement]
      return {
        ...state,
        elements: newElements,
        selectedElementIds: [duplicatedElement.id],
        history: {
          past: [...state.history.past, state.elements],
          present: newElements,
          future: [],
        },
      }
    }

    case "COPY_ELEMENTS": {
      const elementsToCopy = state.elements.filter((el) => action.ids.includes(el.id))
      return { ...state, clipboard: elementsToCopy }
    }

    case "PASTE_ELEMENTS": {
      if (state.clipboard.length === 0) return state

      const basePosition = action.position || { x: 50, y: 50 }
      const maxZIndex = Math.max(...state.elements.map((el) => el.zIndex), 0)

      const pastedElements = state.clipboard.map((el, index) => ({
        ...el,
        id: `${el.id}_paste_${Date.now()}_${index}`,
        x: basePosition.x + index * 20,
        y: basePosition.y + index * 20,
        zIndex: maxZIndex + index + 1,
      }))

      const newElements = [...state.elements, ...pastedElements]
      return {
        ...state,
        elements: newElements,
        selectedElementIds: pastedElements.map((el) => el.id),
        history: {
          past: [...state.history.past, state.elements],
          present: newElements,
          future: [],
        },
      }
    }

    case "BRING_TO_FRONT": {
      const maxZIndex = Math.max(...state.elements.map((el) => el.zIndex))
      const newElements = state.elements.map((el) => (el.id === action.id ? { ...el, zIndex: maxZIndex + 1 } : el))
      return { ...state, elements: newElements }
    }

    case "SEND_TO_BACK": {
      const minZIndex = Math.min(...state.elements.map((el) => el.zIndex))
      const newElements = state.elements.map((el) => (el.id === action.id ? { ...el, zIndex: minZIndex - 1 } : el))
      return { ...state, elements: newElements }
    }

    case "SET_VIEWPORT":
      return { ...state, viewport: { ...state.viewport, ...action.viewport } }

    case "START_DRAG":
      return {
        ...state,
        dragState: {
          isDragging: true,
          draggedElementId: action.elementId,
          startPosition: action.startPosition,
          offset: action.offset,
        },
      }

    case "UPDATE_DRAG": {
      if (!state.dragState.isDragging || !state.dragState.draggedElementId) return state

      const newX = action.position.x - state.dragState.offset.x
      const newY = action.position.y - state.dragState.offset.y

      const newElements = state.elements.map((el) =>
        el.id === state.dragState.draggedElementId ? { ...el, x: newX, y: newY } : el,
      )

      return { ...state, elements: newElements }
    }

    case "END_DRAG": {
      if (!state.dragState.isDragging) return state

      return {
        ...state,
        dragState: {
          isDragging: false,
          draggedElementId: null,
          startPosition: { x: 0, y: 0 },
          offset: { x: 0, y: 0 },
        },
        history: {
          past: [...state.history.past, state.history.present],
          present: state.elements,
          future: [],
        },
      }
    }

    case "UNDO": {
      if (state.history.past.length === 0) return state

      const previous = state.history.past[state.history.past.length - 1]
      const newPast = state.history.past.slice(0, state.history.past.length - 1)

      return {
        ...state,
        elements: previous,
        history: {
          past: newPast,
          present: previous,
          future: [state.history.present, ...state.history.future],
        },
      }
    }

    case "REDO": {
      if (state.history.future.length === 0) return state

      const next = state.history.future[0]
      const newFuture = state.history.future.slice(1)

      return {
        ...state,
        elements: next,
        history: {
          past: [...state.history.past, state.history.present],
          present: next,
          future: newFuture,
        },
      }
    }

    case "SAVE_STATE":
      return {
        ...state,
        history: {
          past: [...state.history.past, state.elements],
          present: state.elements,
          future: [],
        },
      }

    default:
      return state
  }
}

interface CanvasStateContextType {
  state: CanvasState
  dispatch: React.Dispatch<CanvasAction>
  // Helper functions
  addElement: (type: CanvasElement["type"], position: { x: number; y: number }) => void
  updateElement: (id: string, updates: Partial<CanvasElement>) => void
  deleteSelectedElements: () => void
  duplicateSelectedElements: () => void
  copySelectedElements: () => void
  pasteElements: (position?: { x: number; y: number }) => void
  selectElement: (id: string, multiSelect?: boolean) => void
  clearSelection: () => void
  undo: () => void
  redo: () => void
  canUndo: boolean
  canRedo: boolean
  selectedElements: CanvasElement[]
}

const CanvasStateContext = createContext<CanvasStateContextType | undefined>(undefined)

export function CanvasStateProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(canvasReducer, initialState)

  const addElement = useCallback(
    (type: CanvasElement["type"], position: { x: number; y: number }) => {
      const newElement: CanvasElement = {
        id: `element_${Date.now()}`,
        type,
        x: position.x,
        y: position.y,
        width: type === "text" ? 200 : 100,
        height: type === "text" ? 40 : 100,
        rotation: 0,
        opacity: 1,
        zIndex: Math.max(...state.elements.map((el) => el.zIndex), 0) + 1,
        locked: false,
        visible: true,
        properties: {
          ...(type === "text" && {
            content: "Text Element",
            fontSize: 16,
            fontFamily: "Inter",
            fontWeight: "400",
            color: "#000000",
            textAlign: "left" as const,
          }),
          ...(type === "rectangle" && {
            backgroundColor: "#f3f4f6",
            borderColor: "#d1d5db",
            borderWidth: 1,
            borderRadius: 4,
          }),
          ...(type === "circle" && {
            backgroundColor: "#f3f4f6",
            borderColor: "#d1d5db",
            borderWidth: 1,
          }),
          ...(type === "container" && {
            backgroundColor: "transparent",
            padding: 16,
            gap: 8,
            flexDirection: "column" as const,
            justifyContent: "flex-start" as const,
            alignItems: "flex-start" as const,
          }),
        },
      }

      dispatch({ type: "ADD_ELEMENT", element: newElement })
    },
    [state.elements],
  )

  const updateElement = useCallback((id: string, updates: Partial<CanvasElement>) => {
    dispatch({ type: "UPDATE_ELEMENT", id, updates })
  }, [])

  const deleteSelectedElements = useCallback(() => {
    if (state.selectedElementIds.length > 0) {
      dispatch({ type: "DELETE_ELEMENTS", ids: state.selectedElementIds })
    }
  }, [state.selectedElementIds])

  const duplicateSelectedElements = useCallback(() => {
    state.selectedElementIds.forEach((id) => {
      dispatch({ type: "DUPLICATE_ELEMENT", id })
    })
  }, [state.selectedElementIds])

  const copySelectedElements = useCallback(() => {
    dispatch({ type: "COPY_ELEMENTS", ids: state.selectedElementIds })
  }, [state.selectedElementIds])

  const pasteElements = useCallback((position?: { x: number; y: number }) => {
    dispatch({ type: "PASTE_ELEMENTS", position })
  }, [])

  const selectElement = useCallback((id: string, multiSelect = false) => {
    dispatch({ type: "SELECT_ELEMENT", id, multiSelect })
  }, [])

  const clearSelection = useCallback(() => {
    dispatch({ type: "CLEAR_SELECTION" })
  }, [])

  const undo = useCallback(() => {
    dispatch({ type: "UNDO" })
  }, [])

  const redo = useCallback(() => {
    dispatch({ type: "REDO" })
  }, [])

  const canUndo = state.history.past.length > 0
  const canRedo = state.history.future.length > 0
  const selectedElements = state.elements.filter((el) => state.selectedElementIds.includes(el.id))

  return (
    <CanvasStateContext.Provider
      value={{
        state,
        dispatch,
        addElement,
        updateElement,
        deleteSelectedElements,
        duplicateSelectedElements,
        copySelectedElements,
        pasteElements,
        selectElement,
        clearSelection,
        undo,
        redo,
        canUndo,
        canRedo,
        selectedElements,
      }}
    >
      {children}
    </CanvasStateContext.Provider>
  )
}

export function useCanvasState() {
  const context = useContext(CanvasStateContext)
  if (context === undefined) {
    throw new Error("useCanvasState must be used within a CanvasStateProvider")
  }
  return context
}
