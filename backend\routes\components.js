const express = require('express');
const { v4: uuidv4 } = require('uuid');
const storage = require('../utils/storage');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Supported field types
const FIELD_TYPES = {
  TXT: 'text',
  DD: 'dropdown',
  CB: 'checkbox',
  RB: 'radio',
  TA: 'textarea',
  NUM: 'number',
  EMAIL: 'email',
  DATE: 'date',
  FILE: 'file',
  BTN: 'button',
  IMG: 'image',
  DIV: 'container'
};

// Get all components for a specific app
router.get('/app/:appId', authenticateToken, (req, res) => {
  try {
    const { appId } = req.params;
    
    // Verify app ownership
    const apps = storage.getApps();
    const app = apps.find(a => a.id === appId);
    
    if (!app) {
      return res.status(404).json({ error: 'App not found' });
    }
    
    if (app.creator !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    const components = storage.getComponents();
    const appComponents = components.filter(c => c.appId === appId);
    
    res.json({
      components: appComponents,
      total: appComponents.length
    });
  } catch (error) {
    console.error('Get components error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get a specific component by ID
router.get('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const components = storage.getComponents();
    const component = components.find(c => c.id === id);

    if (!component) {
      return res.status(404).json({ error: 'Component not found' });
    }

    // Verify app ownership
    const apps = storage.getApps();
    const app = apps.find(a => a.id === component.appId);
    
    if (!app || app.creator !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({ component });
  } catch (error) {
    console.error('Get component error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create a new component
router.post('/', authenticateToken, (req, res) => {
  try {
    const { appId, field_type, field_name, properties, position } = req.body;

    // Validation
    if (!appId || !field_type || !field_name) {
      return res.status(400).json({ 
        error: 'appId, field_type, and field_name are required' 
      });
    }

    // Verify app ownership
    const apps = storage.getApps();
    const app = apps.find(a => a.id === appId);
    
    if (!app) {
      return res.status(404).json({ error: 'App not found' });
    }
    
    if (app.creator !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Validate field type
    if (!Object.values(FIELD_TYPES).includes(field_type)) {
      return res.status(400).json({ 
        error: `Invalid field_type. Supported types: ${Object.values(FIELD_TYPES).join(', ')}` 
      });
    }

    // Validate field name
    if (field_name.length > 50) {
      return res.status(400).json({ error: 'Field name must be less than 50 characters' });
    }

    // Create new component
    const newComponent = {
      id: uuidv4(),
      appId,
      field_type,
      field_name: field_name.trim(),
      properties: properties || {},
      position: position || { x: 0, y: 0, width: 200, height: 40 },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      order: 0 // For ordering components in the UI
    };

    // Set default properties based on field type
    switch (field_type) {
      case FIELD_TYPES.TXT:
        newComponent.properties = {
          placeholder: 'Enter text...',
          maxLength: 255,
          required: false,
          ...properties
        };
        break;
      case FIELD_TYPES.DD:
        newComponent.properties = {
          options: ['Option 1', 'Option 2', 'Option 3'],
          multiple: false,
          required: false,
          ...properties
        };
        break;
      case FIELD_TYPES.CB:
        newComponent.properties = {
          label: field_name,
          checked: false,
          ...properties
        };
        break;
      case FIELD_TYPES.BTN:
        newComponent.properties = {
          text: field_name,
          variant: 'primary',
          action: 'submit',
          ...properties
        };
        break;
      default:
        newComponent.properties = properties || {};
    }

    // Save component
    const components = storage.getComponents();
    components.push(newComponent);
    storage.saveComponents(components);

    res.status(201).json({
      message: 'Component created successfully',
      component: newComponent
    });

  } catch (error) {
    console.error('Create component error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update an existing component
router.put('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const { field_name, properties, position, order } = req.body;

    const components = storage.getComponents();
    const componentIndex = components.findIndex(c => c.id === id);

    if (componentIndex === -1) {
      return res.status(404).json({ error: 'Component not found' });
    }

    // Verify app ownership
    const apps = storage.getApps();
    const app = apps.find(a => a.id === components[componentIndex].appId);
    
    if (!app || app.creator !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Validation
    if (field_name !== undefined) {
      if (!field_name || field_name.trim().length === 0) {
        return res.status(400).json({ error: 'Field name cannot be empty' });
      }
      if (field_name.length > 50) {
        return res.status(400).json({ error: 'Field name must be less than 50 characters' });
      }
    }

    // Update component
    const updatedComponent = {
      ...components[componentIndex],
      ...(field_name !== undefined && { field_name: field_name.trim() }),
      ...(properties !== undefined && { properties: { ...components[componentIndex].properties, ...properties } }),
      ...(position !== undefined && { position }),
      ...(order !== undefined && { order }),
      updatedAt: new Date().toISOString()
    };

    components[componentIndex] = updatedComponent;
    storage.saveComponents(components);

    res.json({
      message: 'Component updated successfully',
      component: updatedComponent
    });

  } catch (error) {
    console.error('Update component error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete a component
router.delete('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const components = storage.getComponents();
    const componentIndex = components.findIndex(c => c.id === id);

    if (componentIndex === -1) {
      return res.status(404).json({ error: 'Component not found' });
    }

    // Verify app ownership
    const apps = storage.getApps();
    const app = apps.find(a => a.id === components[componentIndex].appId);
    
    if (!app || app.creator !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Remove component
    const deletedComponent = components.splice(componentIndex, 1)[0];
    storage.saveComponents(components);

    res.json({
      message: 'Component deleted successfully',
      component: deletedComponent
    });

  } catch (error) {
    console.error('Delete component error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get available field types
router.get('/types/available', (req, res) => {
  res.json({
    fieldTypes: FIELD_TYPES,
    descriptions: {
      [FIELD_TYPES.TXT]: 'Text input field',
      [FIELD_TYPES.DD]: 'Dropdown selection',
      [FIELD_TYPES.CB]: 'Checkbox input',
      [FIELD_TYPES.RB]: 'Radio button group',
      [FIELD_TYPES.TA]: 'Textarea for long text',
      [FIELD_TYPES.NUM]: 'Number input',
      [FIELD_TYPES.EMAIL]: 'Email input with validation',
      [FIELD_TYPES.DATE]: 'Date picker',
      [FIELD_TYPES.FILE]: 'File upload',
      [FIELD_TYPES.BTN]: 'Button element',
      [FIELD_TYPES.IMG]: 'Image display',
      [FIELD_TYPES.DIV]: 'Container/div element'
    }
  });
});

module.exports = router;
