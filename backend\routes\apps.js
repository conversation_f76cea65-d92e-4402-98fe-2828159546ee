const express = require('express');
const { v4: uuidv4 } = require('uuid');
const storage = require('../utils/storage');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// Get all apps for the authenticated user
router.get('/', authenticateToken, (req, res) => {
  try {
    const apps = storage.getApps();
    const userApps = apps.filter(app => app.creator === req.user.id);
    
    res.json({
      apps: userApps,
      total: userApps.length
    });
  } catch (error) {
    console.error('Get apps error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Get a specific app by ID
router.get('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const apps = storage.getApps();
    const app = apps.find(a => a.id === id);

    if (!app) {
      return res.status(404).json({ error: 'App not found' });
    }

    // Check if user owns the app
    if (app.creator !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    res.json({ app });
  } catch (error) {
    console.error('Get app error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Create a new app
router.post('/', authenticateToken, (req, res) => {
  try {
    const { name, description } = req.body;

    // Validation
    if (!name || name.trim().length === 0) {
      return res.status(400).json({ error: 'App name is required' });
    }

    if (name.length > 100) {
      return res.status(400).json({ error: 'App name must be less than 100 characters' });
    }

    if (description && description.length > 500) {
      return res.status(400).json({ error: 'Description must be less than 500 characters' });
    }

    // Create new app
    const newApp = {
      id: uuidv4(),
      name: name.trim(),
      description: description ? description.trim() : '',
      creator: req.user.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'draft', // draft, published, archived
      components: [], // Will store component IDs
      settings: {
        theme: 'default',
        layout: 'responsive'
      }
    };

    // Save app
    const apps = storage.getApps();
    apps.push(newApp);
    storage.saveApps(apps);

    res.status(201).json({
      message: 'App created successfully',
      app: newApp
    });

  } catch (error) {
    console.error('Create app error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Update an existing app
router.put('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, status, settings } = req.body;

    const apps = storage.getApps();
    const appIndex = apps.findIndex(a => a.id === id);

    if (appIndex === -1) {
      return res.status(404).json({ error: 'App not found' });
    }

    // Check if user owns the app
    if (apps[appIndex].creator !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Validation
    if (name !== undefined) {
      if (!name || name.trim().length === 0) {
        return res.status(400).json({ error: 'App name cannot be empty' });
      }
      if (name.length > 100) {
        return res.status(400).json({ error: 'App name must be less than 100 characters' });
      }
    }

    if (description !== undefined && description.length > 500) {
      return res.status(400).json({ error: 'Description must be less than 500 characters' });
    }

    if (status !== undefined && !['draft', 'published', 'archived'].includes(status)) {
      return res.status(400).json({ error: 'Invalid status. Must be draft, published, or archived' });
    }

    // Update app
    const updatedApp = {
      ...apps[appIndex],
      ...(name !== undefined && { name: name.trim() }),
      ...(description !== undefined && { description: description.trim() }),
      ...(status !== undefined && { status }),
      ...(settings !== undefined && { settings: { ...apps[appIndex].settings, ...settings } }),
      updatedAt: new Date().toISOString()
    };

    apps[appIndex] = updatedApp;
    storage.saveApps(apps);

    res.json({
      message: 'App updated successfully',
      app: updatedApp
    });

  } catch (error) {
    console.error('Update app error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Delete an app
router.delete('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const apps = storage.getApps();
    const appIndex = apps.findIndex(a => a.id === id);

    if (appIndex === -1) {
      return res.status(404).json({ error: 'App not found' });
    }

    // Check if user owns the app
    if (apps[appIndex].creator !== req.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }

    // Remove app
    const deletedApp = apps.splice(appIndex, 1)[0];
    storage.saveApps(apps);

    // Also remove associated components
    const components = storage.getComponents();
    const updatedComponents = components.filter(c => c.appId !== id);
    storage.saveComponents(updatedComponents);

    res.json({
      message: 'App deleted successfully',
      app: deletedApp
    });

  } catch (error) {
    console.error('Delete app error:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

module.exports = router;
