"use client"

import { Card } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { MoreVertical } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

interface Project {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  creatorId: string
  creatorName: string
  creatorEmail: string
}

interface ProjectCardProps {
  project: Project
  onEdit?: (project: Project) => void
  onDelete?: (projectId: string) => void
  onOpen?: (project: Project) => void
}

export function ProjectCard({ project, onEdit, onDelete, onOpen }: ProjectCardProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) return "Viewed today"
    if (diffDays === 1) return "Viewed 1 day ago"
    return `Viewed ${diffDays} days ago`
  }

  return (
    <Card className="bg-gray-200 hover:bg-gray-100 transition-colors cursor-pointer group overflow-hidden">
      <div className="p-0">
        {/* Project Preview Area */}
        <div className="h-48 bg-gray-300 flex items-center justify-center relative" onClick={() => onOpen?.(project)}>
          <div className="text-gray-500 text-sm">Project Preview</div>

          {/* Menu Button */}
          <div className="absolute top-3 right-3">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="opacity-0 group-hover:opacity-100 transition-opacity p-1 h-auto bg-white/80 hover:bg-white"
                  onClick={(e) => e.stopPropagation()}
                >
                  <MoreVertical size={16} className="text-gray-600" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onOpen?.(project)}>Open</DropdownMenuItem>
                <DropdownMenuItem onClick={() => onEdit?.(project)}>Edit</DropdownMenuItem>
                <DropdownMenuItem onClick={() => onDelete?.(project.id)} className="text-red-600">
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Project Info */}
        <div className="p-4 bg-white">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-gray-900 truncate text-base">{project.name || "Project Name"}</h3>
              <div className="flex items-center gap-1 mt-1 text-gray-500 text-sm">
                <span>{formatDate(project.updatedAt)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  )
}
