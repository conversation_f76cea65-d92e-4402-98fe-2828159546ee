"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Trash2,
  Copy,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Plus,
  Type,
  Hash,
  Eye,
  ChevronRight,
  ChevronLeft,
  RotateCcw,
  Lock,
  Unlock,
  Layers,
} from "lucide-react"
import { useCanvasState } from "./canvas-state-context"

interface PropertiesPanelProps {
  selectedElement: any
  isVisible: boolean
  onToggleVisibility: () => void
}

export function PropertiesPanel({ selectedElement, isVisible, onToggleVisibility }: PropertiesPanelProps) {
  const { state, dispatch } = useCanvasState()
  const currentElement =
    selectedElement ||
    (state.selectedElementIds.length > 0 ? state.elements.find((el) => el.id === state.selectedElementIds[0]) : null)

  const handlePropertyChange = (path: string, value: any) => {
    if (!currentElement) return

    dispatch({
      type: "UPDATE_ELEMENT_PROPERTY",
      elementId: currentElement.id,
      path,
      value,
    })
  }

  const duplicateSelectedElements = () => {
    state.selectedElementIds.forEach((id) => {
      dispatch({ type: "DUPLICATE_ELEMENT", id })
    })
  }

  if (!isVisible) {
    return (
      <div className="w-8 bg-gray-900 border-l border-gray-700 flex flex-col items-center py-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleVisibility}
          className="text-gray-400 hover:text-white hover:bg-gray-800 p-2"
        >
          <ChevronLeft size={16} />
        </Button>
      </div>
    )
  }

  return (
    <div className="w-[480px] bg-gray-900 border-l border-gray-700 flex flex-col max-h-screen">
      {/* Header */}
      <div className="p-4 border-b border-gray-700 flex items-center justify-between flex-shrink-0">
        <h2 className="text-white font-medium">Properties</h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleVisibility}
          className="text-gray-400 hover:text-white hover:bg-gray-800 p-2"
        >
          <ChevronRight size={16} />
        </Button>
      </div>

      <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
        <div className="p-5 space-y-6">
          {/* Element Info */}
          <div>
            <h3 className="text-white font-medium mb-3 capitalize">{currentElement?.type || "No Element"} Element</h3>
            <div className="flex gap-2">
              <Button
                size="sm"
                variant="outline"
                onClick={duplicateSelectedElements}
                className="flex-1 bg-gray-800 border-gray-700 text-white hover:bg-gray-700"
                disabled={!currentElement}
              >
                <Copy size={14} className="mr-1" />
                Duplicate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => currentElement && dispatch({ type: "DELETE_ELEMENT", id: currentElement.id })}
                className="bg-red-900 border-red-700 text-red-300 hover:bg-red-800"
                disabled={!currentElement}
              >
                <Trash2 size={14} />
              </Button>
            </div>
          </div>

          {currentElement && (
            <>
              {(currentElement?.type === "text" || currentElement?.type === "button") && (
                <div>
                  <h3 className="text-white font-medium mb-3">Content</h3>
                  <Textarea
                    value={currentElement?.properties.text || currentElement?.properties.content || ""}
                    onChange={(e) => {
                      const prop = currentElement?.type === "text" ? "properties.text" : "properties.content"
                      handlePropertyChange(prop, e.target.value)
                    }}
                    className="bg-gray-800 border-gray-700 text-white resize-none"
                    rows={3}
                    placeholder="Enter text content..."
                  />
                </div>
              )}

              {["input", "checkbox", "dropdown", "radio", "datetime", "searchbox"].includes(currentElement?.type) && (
                <div>
                  <h3 className="text-white font-medium mb-3">Form Properties</h3>
                  <div className="space-y-3">
                    {currentElement?.type === "input" && (
                      <>
                        <div>
                          <label className="block text-xs text-gray-400 mb-1">Placeholder</label>
                          <Input
                            value={currentElement?.properties.placeholder || ""}
                            onChange={(e) => handlePropertyChange("properties.placeholder", e.target.value)}
                            className="bg-gray-800 border-gray-700 text-white h-8"
                          />
                        </div>
                        <div>
                          <label className="block text-xs text-gray-400 mb-1">Input Type</label>
                          <Select
                            value={currentElement?.properties.type || "text"}
                            onValueChange={(value) => handlePropertyChange("properties.type", value)}
                          >
                            <SelectTrigger className="bg-gray-800 border-gray-700 text-white h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="text">Text</SelectItem>
                              <SelectItem value="email">Email</SelectItem>
                              <SelectItem value="password">Password</SelectItem>
                              <SelectItem value="number">Number</SelectItem>
                              <SelectItem value="tel">Phone</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </>
                    )}

                    {currentElement?.type === "checkbox" && (
                      <div>
                        <label className="block text-xs text-gray-400 mb-1">Label</label>
                        <Input
                          value={currentElement?.properties.label || ""}
                          onChange={(e) => handlePropertyChange("properties.label", e.target.value)}
                          className="bg-gray-800 border-gray-700 text-white h-8"
                        />
                      </div>
                    )}

                    {currentElement?.type === "dropdown" && (
                      <div>
                        <label className="block text-xs text-gray-400 mb-1">Options (one per line)</label>
                        <Textarea
                          value={(currentElement?.properties.options || []).join("\n")}
                          onChange={(e) =>
                            handlePropertyChange("properties.options", e.target.value.split("\n").filter(Boolean))
                          }
                          className="bg-gray-800 border-gray-700 text-white resize-none"
                          rows={4}
                        />
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <label className="text-xs text-gray-400">Required</label>
                      <Switch
                        checked={currentElement?.properties.required || false}
                        onCheckedChange={(checked) => handlePropertyChange("properties.required", checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <label className="text-xs text-gray-400">Disabled</label>
                      <Switch
                        checked={currentElement?.properties.disabled || false}
                        onCheckedChange={(checked) => handlePropertyChange("properties.disabled", checked)}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Align */}
              <div>
                <h3 className="text-white font-medium mb-3">Align</h3>
                <div className="flex gap-1 bg-gray-800 p-1.5 rounded-md">
                  {[
                    { icon: AlignLeft, value: "left", tooltip: "Align Left" },
                    { icon: Plus, value: "center-horizontal", tooltip: "Center Horizontal" },
                    { icon: AlignCenter, value: "center", tooltip: "Center" },
                    { icon: Type, value: "text-center", tooltip: "Text Center" },
                    { icon: Plus, value: "center-vertical", tooltip: "Center Vertical" },
                    { icon: AlignRight, value: "right", tooltip: "Align Right" },
                  ].map((align, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      className="flex-1 h-9 text-gray-300 hover:text-white hover:bg-gray-700"
                      onClick={() => {
                        console.log(`Align: ${align.value}`)
                      }}
                      title={align.tooltip}
                    >
                      <align.icon size={14} />
                    </Button>
                  ))}
                </div>
              </div>

              {/* Spacing */}
              <div>
                <h3 className="text-white font-medium mb-3">Spacing</h3>
                <div className="relative">
                  {/* Top */}
                  <div className="flex justify-center mb-3">
                    <Input
                      type="number"
                      value={currentElement?.properties?.marginTop ?? 0}
                      onChange={(e) => handlePropertyChange("properties.marginTop", Number(e.target.value) || 0)}
                      className="w-18 h-9 bg-gray-800 border-gray-700 text-white text-center text-sm"
                    />
                  </div>

                  {/* Middle row with left, center box, right */}
                  <div className="flex items-center gap-3 mb-3">
                    <Input
                      type="number"
                      value={currentElement?.properties?.marginLeft ?? 0}
                      onChange={(e) => handlePropertyChange("properties.marginLeft", Number(e.target.value) || 0)}
                      className="w-18 h-9 bg-gray-800 border-gray-700 text-white text-center text-sm"
                    />

                    {/* Center spacing box */}
                    <div className="flex-1 bg-gray-700 border border-gray-600 rounded-lg p-3 min-h-[80px] relative">
                      <div className="absolute inset-3 border border-gray-500 rounded flex items-center justify-center">
                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <Input
                            type="number"
                            value={currentElement?.properties?.paddingTop ?? 0}
                            onChange={(e) => handlePropertyChange("properties.paddingTop", Number(e.target.value) || 0)}
                            className="w-10 h-7 bg-gray-800 border-gray-600 text-white text-center text-xs p-0"
                          />
                          <Input
                            type="number"
                            value={currentElement?.properties?.paddingRight ?? 0}
                            onChange={(e) =>
                              handlePropertyChange("properties.paddingRight", Number(e.target.value) || 0)
                            }
                            className="w-10 h-7 bg-gray-800 border-gray-600 text-white text-center text-xs p-0"
                          />
                          <Input
                            type="number"
                            value={currentElement?.properties?.paddingBottom ?? 0}
                            onChange={(e) =>
                              handlePropertyChange("properties.paddingBottom", Number(e.target.value) || 0)
                            }
                            className="w-10 h-7 bg-gray-800 border-gray-600 text-white text-center text-xs p-0"
                          />
                          <Input
                            type="number"
                            value={currentElement?.properties?.paddingLeft ?? 0}
                            onChange={(e) =>
                              handlePropertyChange("properties.paddingLeft", Number(e.target.value) || 0)
                            }
                            className="w-10 h-7 bg-gray-800 border-gray-600 text-white text-center text-xs p-0"
                          />
                        </div>
                      </div>
                    </div>

                    <Input
                      type="number"
                      value={currentElement?.properties?.marginRight ?? 0}
                      onChange={(e) => handlePropertyChange("properties.marginRight", Number(e.target.value) || 0)}
                      className="w-18 h-9 bg-gray-800 border-gray-700 text-white text-center text-sm"
                    />
                  </div>

                  {/* Bottom */}
                  <div className="flex justify-center">
                    <Input
                      type="number"
                      value={currentElement?.properties?.marginBottom ?? 0}
                      onChange={(e) => handlePropertyChange("properties.marginBottom", Number(e.target.value) || 0)}
                      className="w-18 h-9 bg-gray-800 border-gray-700 text-white text-center text-sm"
                    />
                  </div>
                </div>
              </div>

              {/* Typography */}
              {(currentElement?.type === "text" || currentElement?.type === "button") && (
                <div>
                  <h3 className="text-white font-medium mb-3">Typography</h3>
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      <Select
                        value={currentElement?.properties.fontFamily || "Inter"}
                        onValueChange={(value) => handlePropertyChange("properties.fontFamily", value)}
                      >
                        <SelectTrigger className="bg-gray-800 border-gray-700 text-white h-9">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Inter">Inter</SelectItem>
                          <SelectItem value="Roboto">Roboto</SelectItem>
                          <SelectItem value="Arial">Arial</SelectItem>
                          <SelectItem value="Georgia">Georgia</SelectItem>
                          <SelectItem value="Helvetica">Helvetica</SelectItem>
                        </SelectContent>
                      </Select>

                      <Select
                        value={currentElement?.properties.fontWeight || "400"}
                        onValueChange={(value) => handlePropertyChange("properties.fontWeight", value)}
                      >
                        <SelectTrigger className="bg-gray-800 border-gray-700 text-white h-9">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="300">Light</SelectItem>
                          <SelectItem value="400">Regular</SelectItem>
                          <SelectItem value="600">Semi Bold</SelectItem>
                          <SelectItem value="700">Bold</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <Select
                        value={String(currentElement?.properties.fontSize || 16)}
                        onValueChange={(value) => handlePropertyChange("properties.fontSize", Number(value))}
                      >
                        <SelectTrigger className="bg-gray-800 border-gray-700 text-white h-9">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="12">12</SelectItem>
                          <SelectItem value="14">14</SelectItem>
                          <SelectItem value="16">16</SelectItem>
                          <SelectItem value="18">18</SelectItem>
                          <SelectItem value="20">20</SelectItem>
                          <SelectItem value="22">22</SelectItem>
                          <SelectItem value="24">24</SelectItem>
                          <SelectItem value="28">28</SelectItem>
                          <SelectItem value="32">32</SelectItem>
                        </SelectContent>
                      </Select>

                      <div className="flex items-center gap-2">
                        <Hash size={12} className="text-gray-400" />
                        <Input
                          type="number"
                          value={currentElement?.properties?.letterSpacing ?? 0}
                          onChange={(e) =>
                            handlePropertyChange("properties.letterSpacing", Number(e.target.value) || 0)
                          }
                          className="bg-gray-800 border-gray-700 text-white h-9 text-sm"
                          placeholder="0%"
                        />
                      </div>
                    </div>

                    {/* Text Alignment */}
                    <div className="flex gap-1 bg-gray-800 p-1.5 rounded-md">
                      {[
                        { icon: AlignLeft, value: "left" },
                        { icon: Plus, value: "center" },
                        { icon: AlignCenter, value: "center" },
                        { icon: Type, value: "justify" },
                        { icon: Plus, value: "right" },
                        { icon: AlignRight, value: "right" },
                      ].map((align, index) => (
                        <Button
                          key={index}
                          variant="ghost"
                          size="sm"
                          className="flex-1 h-9 text-gray-300 hover:text-white hover:bg-gray-700"
                          onClick={() => handlePropertyChange("properties.textAlign", align.value)}
                        >
                          <align.icon size={14} />
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* Fill */}
              <div>
                <h3 className="text-white font-medium mb-3">Fill</h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="flex items-center gap-2 flex-1 bg-gray-800 rounded-md p-2">
                      <div
                        className="w-4 h-4 rounded border border-gray-600"
                        style={{
                          backgroundColor:
                            currentElement?.properties.backgroundColor || currentElement?.properties.color || "#FFFFFF",
                        }}
                      />
                      <Input
                        type="text"
                        value={
                          currentElement?.properties.backgroundColor || currentElement?.properties.color || "#FFFFFF"
                        }
                        onChange={(e) => {
                          const property =
                            currentElement?.type === "text" ? "properties.color" : "properties.backgroundColor"
                          handlePropertyChange(property, e.target.value)
                        }}
                        className="bg-transparent border-none text-white text-sm flex-1 p-0 h-auto"
                      />
                      <span className="text-gray-400 text-sm">100 %</span>
                    </div>
                    <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white">
                      <Eye size={16} />
                    </Button>
                  </div>

                  <div className="flex items-center justify-between">
                    <label className="text-xs text-gray-400">Enable Gradient</label>
                    <Switch
                      checked={currentElement?.properties.useGradient || false}
                      onCheckedChange={(checked) => handlePropertyChange("properties.useGradient", checked)}
                    />
                  </div>

                  {currentElement?.properties.useGradient && (
                    <div className="space-y-2">
                      <Input
                        type="text"
                        value={currentElement?.properties.gradientFrom || "#3B82F6"}
                        onChange={(e) => handlePropertyChange("properties.gradientFrom", e.target.value)}
                        className="bg-gray-800 border-gray-700 text-white h-8"
                        placeholder="From color"
                      />
                      <Input
                        type="text"
                        value={currentElement?.properties.gradientTo || "#1E40AF"}
                        onChange={(e) => handlePropertyChange("properties.gradientTo", e.target.value)}
                        className="bg-gray-800 border-gray-700 text-white h-8"
                        placeholder="To color"
                      />
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-white font-medium mb-3">Border</h3>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-2">
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">Width</label>
                      <Input
                        type="number"
                        value={currentElement?.properties?.borderWidth ?? 0}
                        onChange={(e) => handlePropertyChange("properties.borderWidth", Number(e.target.value) || 0)}
                        className="bg-gray-800 border-gray-700 text-white h-8"
                      />
                    </div>
                    <div>
                      <label className="block text-xs text-gray-400 mb-1">Style</label>
                      <Select
                        value={currentElement?.properties.borderStyle || "solid"}
                        onValueChange={(value) => handlePropertyChange("properties.borderStyle", value)}
                      >
                        <SelectTrigger className="bg-gray-800 border-gray-700 text-white h-8">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="solid">Solid</SelectItem>
                          <SelectItem value="dashed">Dashed</SelectItem>
                          <SelectItem value="dotted">Dotted</SelectItem>
                          <SelectItem value="double">Double</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Color</label>
                    <Input
                      type="text"
                      value={currentElement?.properties.borderColor || "#000000"}
                      onChange={(e) => handlePropertyChange("properties.borderColor", e.target.value)}
                      className="bg-gray-800 border-gray-700 text-white h-8"
                    />
                  </div>
                </div>
              </div>

              {/* Appearance */}
              <div>
                <h3 className="text-white font-medium mb-3">Appearance</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Opacity</label>
                    <div className="flex items-center gap-2">
                      <Hash size={12} className="text-gray-400" />
                      <Input
                        type="number"
                        value={Math.round((currentElement?.opacity ?? 1) * 100)}
                        onChange={(e) => handlePropertyChange("opacity", (Number(e.target.value) || 0) / 100)}
                        className="bg-gray-800 border-gray-700 text-white h-8 text-xs flex-1"
                        min="0"
                        max="100"
                      />
                      <span className="text-gray-400 text-xs">%</span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Corner Radius</label>
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border border-gray-600 rounded flex items-center justify-center">
                        <div className="w-2 h-2 border border-gray-500 rounded-sm" />
                      </div>
                      <Input
                        type="number"
                        value={currentElement?.properties.borderRadius || 0}
                        onChange={(e) => handlePropertyChange("properties.borderRadius", Number(e.target.value))}
                        className="bg-gray-800 border-gray-700 text-white h-8 text-xs flex-1"
                      />
                    </div>
                  </div>
                </div>

                <div className="mt-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="text-xs text-gray-400">Drop Shadow</label>
                    <Switch
                      checked={currentElement?.properties.hasShadow || false}
                      onCheckedChange={(checked) => handlePropertyChange("properties.hasShadow", checked)}
                    />
                  </div>

                  {currentElement?.properties.hasShadow && (
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        type="number"
                        value={currentElement?.properties.shadowX || 0}
                        onChange={(e) => handlePropertyChange("properties.shadowX", Number(e.target.value))}
                        className="bg-gray-800 border-gray-700 text-white h-8 text-xs"
                        placeholder="X offset"
                      />
                      <Input
                        type="number"
                        value={currentElement?.properties.shadowY || 4}
                        onChange={(e) => handlePropertyChange("properties.shadowY", Number(e.target.value))}
                        className="bg-gray-800 border-gray-700 text-white h-8 text-xs"
                        placeholder="Y offset"
                      />
                      <Input
                        type="number"
                        value={currentElement?.properties.shadowBlur || 8}
                        onChange={(e) => handlePropertyChange("properties.shadowBlur", Number(e.target.value))}
                        className="bg-gray-800 border-gray-700 text-white h-8 text-xs"
                        placeholder="Blur"
                      />
                      <Input
                        type="text"
                        value={currentElement?.properties.shadowColor || "#000000"}
                        onChange={(e) => handlePropertyChange("properties.shadowColor", e.target.value)}
                        className="bg-gray-800 border-gray-700 text-white h-8 text-xs"
                        placeholder="Color"
                      />
                    </div>
                  )}
                </div>
              </div>

              {/* Position & Size */}
              <div>
                <h3 className="text-white font-medium mb-3">Position & Size</h3>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">X</label>
                    <Input
                      type="number"
                      value={Math.round(currentElement?.x ?? 0)}
                      onChange={(e) => handlePropertyChange("x", Number(e.target.value) || 0)}
                      className="bg-gray-800 border-gray-700 text-white h-9"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Y</label>
                    <Input
                      type="number"
                      value={Math.round(currentElement?.y ?? 0)}
                      onChange={(e) => handlePropertyChange("y", Number(e.target.value) || 0)}
                      className="bg-gray-800 border-gray-700 text-white h-9"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Width</label>
                    <Input
                      type="number"
                      value={Math.round(currentElement?.width ?? 100)}
                      onChange={(e) => handlePropertyChange("width", Number(e.target.value) || 100)}
                      className="bg-gray-800 border-gray-700 text-white h-9"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Height</label>
                    <Input
                      type="number"
                      value={Math.round(currentElement?.height ?? 50)}
                      onChange={(e) => handlePropertyChange("height", Number(e.target.value) || 50)}
                      className="bg-gray-800 border-gray-700 text-white h-9"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 mt-3">
                  <div>
                    <label className="block text-xs text-gray-400 mb-1">Rotation</label>
                    <div className="flex items-center gap-2">
                      <RotateCcw size={12} className="text-gray-400" />
                      <Input
                        type="number"
                        value={currentElement?.properties?.rotation ?? 0}
                        onChange={(e) => handlePropertyChange("properties.rotation", Number(e.target.value) || 0)}
                        className="bg-gray-800 border-gray-700 text-white h-9 text-sm flex-1"
                      />
                      <span className="text-gray-400 text-xs">°</span>
                    </div>
                  </div>
                  <div className="flex items-end">
                    <div className="flex items-center justify-between w-full">
                      <label className="text-xs text-gray-400">Lock</label>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-400 hover:text-white h-9 w-9 p-0"
                        onClick={() => handlePropertyChange("properties.locked", !currentElement?.properties.locked)}
                      >
                        {currentElement?.properties.locked ? <Lock size={14} /> : <Unlock size={14} />}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-white font-medium mb-3">Layer</h3>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1 bg-gray-800 border-gray-700 text-white hover:bg-gray-700"
                    onClick={() => dispatch({ type: "BRING_TO_FRONT", id: currentElement?.id })}
                  >
                    <Layers size={14} className="mr-1" />
                    Front
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1 bg-gray-800 border-gray-700 text-white hover:bg-gray-700"
                    onClick={() => dispatch({ type: "SEND_TO_BACK", id: currentElement?.id })}
                  >
                    <Layers size={14} className="mr-1" />
                    Back
                  </Button>
                </div>
              </div>
            </>
          )}

          {!currentElement && (
            <div className="text-center py-8">
              <div className="text-gray-400 text-sm">Select an element to view its properties</div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
