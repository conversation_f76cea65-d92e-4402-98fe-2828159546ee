import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Authentication API
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  verify: () => api.get('/auth/verify'),
  getProfile: () => api.get('/auth/profile'),
};

// Apps API
export const appsAPI = {
  getAll: () => api.get('/apps'),
  getById: (id) => api.get(`/apps/${id}`),
  create: (appData) => api.post('/apps', appData),
  update: (id, appData) => api.put(`/apps/${id}`, appData),
  delete: (id) => api.delete(`/apps/${id}`),
};

// Components API
export const componentsAPI = {
  getByAppId: (appId) => api.get(`/components/app/${appId}`),
  getById: (id) => api.get(`/components/${id}`),
  create: (componentData) => api.post('/components', componentData),
  update: (id, componentData) => api.put(`/components/${id}`, componentData),
  delete: (id) => api.delete(`/components/${id}`),
  getAvailableTypes: () => api.get('/components/types/available'),
};

// Health check API
export const healthAPI = {
  check: () => api.get('/health'),
};

export default api;
