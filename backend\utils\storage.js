const fs = require('fs');
const path = require('path');
const config = require('../config/config');

class Storage {
  constructor() {
    this.ensureDataDirectory();
    this.initializeFiles();
  }

  ensureDataDirectory() {
    const dataDir = config.storage.dataDir;
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
      console.log(`📁 Created data directory: ${dataDir}`);
    }
  }

  initializeFiles() {
    const files = [
      { path: config.storage.usersFile, defaultData: [] },
      { path: config.storage.appsFile, defaultData: [] },
      { path: config.storage.componentsFile, defaultData: [] }
    ];

    files.forEach(file => {
      if (!fs.existsSync(file.path)) {
        this.writeFile(file.path, file.defaultData);
        console.log(`📄 Created file: ${file.path}`);
      }
    });
  }

  readFile(filePath) {
    try {
      const data = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error(`Error reading file ${filePath}:`, error);
      return [];
    }
  }

  writeFile(filePath, data) {
    try {
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
      return true;
    } catch (error) {
      console.error(`Error writing file ${filePath}:`, error);
      return false;
    }
  }

  // User operations
  getUsers() {
    return this.readFile(config.storage.usersFile);
  }

  saveUsers(users) {
    return this.writeFile(config.storage.usersFile, users);
  }

  // App operations
  getApps() {
    return this.readFile(config.storage.appsFile);
  }

  saveApps(apps) {
    return this.writeFile(config.storage.appsFile, apps);
  }

  // Component operations
  getComponents() {
    return this.readFile(config.storage.componentsFile);
  }

  saveComponents(components) {
    return this.writeFile(config.storage.componentsFile, components);
  }
}

module.exports = new Storage();
