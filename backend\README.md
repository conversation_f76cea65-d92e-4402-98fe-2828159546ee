# RapidGeniAI Backend

Backend API for the RapidGeniAI Low-Code/No-Code App Builder Platform.

## Features

- **JWT-based Authentication**: Secure user registration and login
- **App Management**: CRUD operations for user apps
- **Component System**: Drag-and-drop component management
- **File-based Storage**: Uses JSON files instead of database
- **Security**: Rate limiting, CORS, input validation

## Quick Start

1. **Install Dependencies**
   ```bash
   cd backend
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```

3. **Start Production Server**
   ```bash
   npm start
   ```

The server will run on `http://localhost:5000`

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login user
- `GET /api/auth/profile` - Get user profile (requires auth)
- `GET /api/auth/verify` - Verify JWT token

### Apps
- `GET /api/apps` - Get user's apps (requires auth)
- `GET /api/apps/:id` - Get specific app (requires auth)
- `POST /api/apps` - Create new app (requires auth)
- `PUT /api/apps/:id` - Update app (requires auth)
- `DELETE /api/apps/:id` - Delete app (requires auth)

### Components
- `GET /api/components/app/:appId` - Get app components (requires auth)
- `GET /api/components/:id` - Get specific component (requires auth)
- `POST /api/components` - Create component (requires auth)
- `PUT /api/components/:id` - Update component (requires auth)
- `DELETE /api/components/:id` - Delete component (requires auth)
- `GET /api/components/types/available` - Get available field types

### Health Check
- `GET /api/health` - Server health status

## Data Storage

Data is stored in JSON files in the `./data` directory:
- `users.json` - User accounts
- `apps.json` - User applications
- `components.json` - App components

## Supported Component Types

- **TXT** - Text input field
- **DD** - Dropdown selection
- **CB** - Checkbox input
- **RB** - Radio button group
- **TA** - Textarea for long text
- **NUM** - Number input
- **EMAIL** - Email input with validation
- **DATE** - Date picker
- **FILE** - File upload
- **BTN** - Button element
- **IMG** - Image display
- **DIV** - Container/div element

## Environment Variables

- `PORT` - Server port (default: 5000)
- `JWT_SECRET` - JWT signing secret
- `NODE_ENV` - Environment (development/production)

## Security Features

- Password hashing with bcrypt
- JWT token authentication
- Rate limiting (100 requests per 15 minutes)
- CORS protection
- Input validation and sanitization
- Helmet security headers
