"use client"

import { useCollaboration } from "./collaboration-context"

export function UserCursors() {
  const { cursors } = useCollaboration()

  return (
    <>
      {cursors.map((cursor) => (
        <div
          key={cursor.userId}
          className="absolute pointer-events-none z-50 transition-all duration-200"
          style={{
            left: cursor.x,
            top: cursor.y,
            transform: "translate(-2px, -2px)",
          }}
        >
          {/* Cursor */}
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path
              d="M5.65376 12.3673H5.46026L5.31717 12.4976L0.500002 16.8829L0.500002 1.19841L11.7841 12.3673H5.65376Z"
              fill={cursor.user.color}
              stroke="white"
              strokeWidth="1"
            />
          </svg>

          {/* User label */}
          <div
            className="absolute top-5 left-2 px-2 py-1 rounded text-white text-xs whitespace-nowrap shadow-lg"
            style={{ backgroundColor: cursor.user.color }}
          >
            {cursor.user.name}
          </div>
        </div>
      ))}
    </>
  )
}
