"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Undo, Redo, Save } from "lucide-react"

export function EditorToolbar() {
  return (
    <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center gap-2">
      <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white hover:bg-gray-700">
        <Undo size={16} />
      </Button>
      <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white hover:bg-gray-700">
        <Redo size={16} />
      </Button>
      <div className="w-px h-6 bg-gray-700 mx-2" />
      <Button variant="ghost" size="sm" className="text-gray-400 hover:text-white hover:bg-gray-700">
        <Save size={16} />
      </Button>
    </div>
  )
}
