# RapidGeniAI Frontend

React.js frontend for the RapidGeniAI Low-Code/No-Code App Builder Platform.

## Features

- **React Router**: Navigation between pages
- **JWT Authentication**: Secure login with token storage
- **Drag & Drop**: Component builder using react-dnd
- **Responsive Design**: Works on desktop and mobile
- **Real-time Updates**: Live component editing
- **Toast Notifications**: User feedback with react-hot-toast

## Quick Start

1. **Install Dependencies**
   ```bash
   cd frontend
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm start
   ```

The app will run on `http://localhost:3000`

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── AppCard.js      # App display card
│   ├── CanvasArea.js   # Drag-drop canvas
│   ├── ComponentPalette.js  # Component library
│   ├── ComponentRenderer.js # Component preview
│   ├── CreateAppModal.js    # App creation modal
│   ├── LoadingSpinner.js    # Loading indicator
│   ├── Navbar.js       # Navigation bar
│   └── PropertiesPanel.js   # Component properties editor
├── contexts/           # React contexts
│   └── AuthContext.js  # Authentication state
├── pages/              # Page components
│   ├── AppBuilder.js   # Main app builder interface
│   ├── Dashboard.js    # User dashboard
│   ├── Login.js        # Login page
│   └── Register.js     # Registration page
├── services/           # API services
│   └── api.js          # Axios configuration and API calls
├── App.js              # Main app component
├── index.js            # App entry point
└── index.css           # Global styles
```

## Available Scripts

- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm eject` - Eject from Create React App

## Key Dependencies

- **React 18** - UI library
- **React Router DOM** - Client-side routing
- **React DnD** - Drag and drop functionality
- **Axios** - HTTP client for API calls
- **React Hot Toast** - Toast notifications
- **Lucide React** - Icon library

## Environment Variables

Create a `.env` file in the frontend directory:

```
REACT_APP_API_URL=http://localhost:5000/api
```

## API Integration

The frontend communicates with the backend through:

- **Authentication**: Login, register, token verification
- **Apps**: CRUD operations for user applications
- **Components**: Manage drag-drop components
- **Real-time Updates**: Live component property changes

## Component Types Supported

- Text Input, Textarea, Number, Email, Date
- Dropdown, Checkbox, Radio buttons
- Buttons, Images, Containers
- File upload fields

## Drag & Drop Features

- Drag components from palette to canvas
- Visual drop indicators
- Component selection and editing
- Real-time property updates
- Position and size controls
