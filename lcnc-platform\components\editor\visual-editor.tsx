"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Play, Monitor, User } from "lucide-react"
import { EditorSidebar } from "./editor-sidebar"
import { EditorCanvas } from "./editor-canvas"
import { PropertiesPanel } from "./properties-panel"
import { WorkflowPanel } from "./workflow-panel" // Added workflow panel import
import { CollaborationPanel } from "../collaboration/collaboration-panel"
import { UserCursors } from "../collaboration/user-cursors"
import { CommentsOverlay } from "../collaboration/comments-overlay"
import { useCollaboration } from "../collaboration/collaboration-context"
import { useCanvasState } from "./canvas-state-context" // Added canvas state import

interface Project {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  creatorId: string
  creatorName: string
  creatorEmail: string
}

interface VisualEditorProps {
  project: Project
}

export function VisualEditor({ project }: VisualEditorProps) {
  const [selectedElement, setSelectedElement] = useState<any>(null)
  const [canvasSize, setCanvasSize] = useState({ width: 1440, height: 900 })
  const [isPropertiesPanelVisible, setIsPropertiesPanelVisible] = useState(true)
  const [isPreviewMode, setIsPreviewMode] = useState(false)
  const [showWorkflow, setShowWorkflow] = useState(false) // Added workflow toggle state
  const { activeUsers, isCollaborating } = useCollaboration()
  const { state } = useCanvasState() // Added canvas state for selected elements

  const selectedElements = state.elements.filter((el) => state.selectedElementIds.includes(el.id))

  return (
    <div className="h-screen bg-gray-800 flex flex-col">
      {/* Top Toolbar */}
      <div className="bg-gray-800 border-b border-gray-700 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center gap-4">
          {/* Screen Size Selector */}
          <div className="flex items-center gap-2 bg-gray-700 rounded-lg px-3 py-2">
            <Monitor size={16} className="text-gray-300" />
            <Select
              value={`${canvasSize.width}x${canvasSize.height}`}
              onValueChange={(value) => {
                const [width, height] = value.split("x").map(Number)
                setCanvasSize({ width, height })
              }}
            >
              <SelectTrigger className="bg-transparent border-none text-white text-sm min-w-[120px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1440x900">Sizetext 1440 x 900</SelectItem>
                <SelectItem value="1920x1080">Desktop 1920 x 1080</SelectItem>
                <SelectItem value="768x1024">Tablet 768 x 1024</SelectItem>
                <SelectItem value="375x667">Mobile 375 x 667</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {isCollaborating && (
            <div className="flex items-center gap-2 text-gray-300 text-sm">
              <div className="flex -space-x-2">
                {activeUsers.slice(0, 3).map((user) => (
                  <div
                    key={user.id}
                    className="w-6 h-6 rounded-full border-2 border-gray-800 flex items-center justify-center text-white text-xs font-medium"
                    style={{ backgroundColor: user.color }}
                    title={user.name}
                  >
                    {user.name.charAt(0)}
                  </div>
                ))}
                {activeUsers.length > 3 && (
                  <div className="w-6 h-6 rounded-full border-2 border-gray-800 bg-gray-600 flex items-center justify-center text-white text-xs">
                    +{activeUsers.length - 3}
                  </div>
                )}
              </div>
              <span>{activeUsers.length} online</span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-3">
          <CollaborationPanel />

          {!isPreviewMode && (
            <Button
              variant="outline"
              size="sm"
              className={`${showWorkflow ? "bg-green-600 border-green-500 text-white" : "bg-gray-700 border-gray-600 text-white hover:bg-gray-600"}`}
              onClick={() => setShowWorkflow(!showWorkflow)}
            >
              {showWorkflow ? "Hide Workflow" : "Show Workflow"}
            </Button>
          )}

          {/* Preview Button */}
          <Button
            variant="outline"
            size="sm"
            className={`${isPreviewMode ? "bg-blue-600 border-blue-500 text-white" : "bg-gray-700 border-gray-600 text-white hover:bg-gray-600"}`}
            onClick={() => setIsPreviewMode(!isPreviewMode)}
          >
            <Play size={16} className="mr-2" />
            {isPreviewMode ? "Exit Preview" : "Preview"}
          </Button>

          {/* Publish Button */}
          <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
            Publish
          </Button>
        </div>
      </div>

      {/* Main Editor Area */}
      <div className="flex-1 flex">
        {/* Left Sidebar - Tools and Workflow */}
        {!isPreviewMode && (
          <div className="flex">
            <EditorSidebar />
            {showWorkflow && <WorkflowPanel selectedElements={selectedElements} />}
          </div>
        )}

        {/* Canvas Area */}
        <div className="flex-1 flex flex-col relative">
          <EditorCanvas
            canvasSize={canvasSize}
            selectedElement={selectedElement}
            onSelectElement={setSelectedElement}
          />
          <UserCursors />
          <CommentsOverlay />
        </div>

        {/* Right Sidebar - Properties */}
        {!isPreviewMode && (
          <PropertiesPanel
            selectedElement={selectedElement}
            isVisible={isPropertiesPanelVisible}
            onToggleVisibility={() => setIsPropertiesPanelVisible(!isPropertiesPanelVisible)}
          />
        )}
      </div>

      {/* Bottom User Info */}
      <div className="bg-gray-900 px-4 py-2 border-t border-gray-700">
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 bg-gray-600 rounded-full flex items-center justify-center">
            <User size={12} className="text-gray-300" />
          </div>
          <span className="text-gray-300 text-sm">{project.creatorName}</span>
        </div>
      </div>
    </div>
  )
}
