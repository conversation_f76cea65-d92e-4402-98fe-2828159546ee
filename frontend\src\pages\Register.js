import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';

const Register = () => {
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: ''
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const { register } = useAuth();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    
    // Clear error for this field when user starts typing
    if (errors[e.target.name]) {
      setErrors({
        ...errors,
        [e.target.name]: ''
      });
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Username is required';
    } else if (formData.username.length < 3) {
      newErrors.username = 'Username must be at least 3 characters';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    const { confirmPassword, ...userData } = formData;
    const result = await register(userData);
    
    setLoading(false);
    
    if (!result.success) {
      // Error is already handled by the auth context with toast
      console.error('Registration failed:', result.error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center py-16 px-6 animate-fadeIn">
      <div className="max-w-lg w-full">
        {/* Header Section */}
        <div className="text-center mb-12 animate-slideIn">
          <div className="relative inline-block mb-8">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl blur-xl opacity-30 animate-pulse"></div>
            <div className="relative inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl shadow-2xl">
              <span className="text-white font-bold text-3xl">RG</span>
            </div>
          </div>
          <h1 className="text-5xl font-black bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent mb-4 leading-tight">
            RapidGeniAI
          </h1>
          <h2 className="text-2xl text-white/90 mb-4 font-light">
            Join the Platform
          </h2>
          <p className="text-white/70 text-lg">
            Create your account and start building amazing applications
          </p>
          <div className="mt-6 flex justify-center">
            <div className="h-1 w-24 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
          </div>
        </div>

        {/* Registration Form */}
        <div className="relative animate-slideIn" style={{ animationDelay: '0.3s' }}>
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-xl"></div>
          <div className="relative card border-2 border-white/20 hover:border-white/30 transition-all duration-300">
            <form className="space-y-8" onSubmit={handleSubmit}>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="form-group">
                    <label htmlFor="firstName" className="form-label text-gray-700 text-base font-semibold">
                      First Name
                    </label>
                    <input
                      id="firstName"
                      name="firstName"
                      type="text"
                      className="form-input text-base py-4 px-5 border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500 rounded-xl"
                      placeholder="Enter your first name"
                      value={formData.firstName}
                      onChange={handleChange}
                      disabled={loading}
                    />
                  </div>

                  <div className="form-group">
                    <label htmlFor="lastName" className="form-label text-gray-700 text-base font-semibold">
                      Last Name
                    </label>
                    <input
                      id="lastName"
                      name="lastName"
                      type="text"
                      className="form-input text-base py-4 px-5 border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500 rounded-xl"
                      placeholder="Enter your last name"
                      value={formData.lastName}
                      onChange={handleChange}
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label htmlFor="username" className="form-label text-gray-700 text-base font-semibold">
                    Username *
                  </label>
                  <input
                    id="username"
                    name="username"
                    type="text"
                    required
                    className={`form-input text-base py-4 px-5 border-2 rounded-xl ${errors.username ? 'border-red-500' : 'border-gray-200 hover:border-blue-300 focus:border-blue-500'}`}
                    placeholder="Choose a unique username"
                    value={formData.username}
                    onChange={handleChange}
                    disabled={loading}
                  />
                  {errors.username && (
                    <p className="text-red-500 text-sm mt-2 font-medium">{errors.username}</p>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="email" className="form-label text-gray-700 text-base font-semibold">
                    Email *
                  </label>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    required
                    className={`form-input text-base py-4 px-5 border-2 rounded-xl ${errors.email ? 'border-red-500' : 'border-gray-200 hover:border-blue-300 focus:border-blue-500'}`}
                    placeholder="Enter your email address"
                    value={formData.email}
                    onChange={handleChange}
                    disabled={loading}
                  />
                  {errors.email && (
                    <p className="text-red-500 text-sm mt-2 font-medium">{errors.email}</p>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="password" className="form-label text-gray-700 text-base font-semibold">
                    Password *
                  </label>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    required
                    className={`form-input text-base py-4 px-5 border-2 rounded-xl ${errors.password ? 'border-red-500' : 'border-gray-200 hover:border-blue-300 focus:border-blue-500'}`}
                    placeholder="Create a secure password"
                    value={formData.password}
                    onChange={handleChange}
                    disabled={loading}
                  />
                  {errors.password && (
                    <p className="text-red-500 text-sm mt-2 font-medium">{errors.password}</p>
                  )}
                </div>

                <div className="form-group">
                  <label htmlFor="confirmPassword" className="form-label text-gray-700 text-base font-semibold">
                    Confirm Password *
                  </label>
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    required
                    className={`form-input text-base py-4 px-5 border-2 rounded-xl ${errors.confirmPassword ? 'border-red-500' : 'border-gray-200 hover:border-blue-300 focus:border-blue-500'}`}
                    placeholder="Confirm your password"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    disabled={loading}
                  />
                  {errors.confirmPassword && (
                    <p className="text-red-500 text-sm mt-2 font-medium">{errors.confirmPassword}</p>
                  )}
                </div>
              </div>

              <div className="pt-4">
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full btn btn-primary py-5 text-lg font-bold shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
                >
                  {loading ? (
                    <>
                      <LoadingSpinner size="small" />
                      Creating account...
                    </>
                  ) : (
                    'Create Account'
                  )}
                </button>
              </div>

              <div className="text-center pt-6 border-t border-gray-100">
                <p className="text-base text-gray-600">
                  Already have an account?{' '}
                  <Link
                    to="/login"
                    className="font-bold text-blue-600 hover:text-blue-500 transition-colors hover:underline"
                  >
                    Sign in here
                  </Link>
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Register;
