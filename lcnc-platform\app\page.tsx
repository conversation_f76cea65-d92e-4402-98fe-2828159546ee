import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-100 via-purple-50 to-purple-100 flex items-center justify-center p-4">
      <div className="text-center space-y-8">
        <div className="space-y-4">
          <h1 className="text-4xl font-bold text-gray-900">Welcome to LCNC Platform</h1>
          <p className="text-lg text-gray-600 max-w-md mx-auto">
            Build powerful applications without code using our intuitive drag-and-drop interface.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/auth/signin">
            <Button size="lg" className="w-full sm:w-auto">
              Sign In
            </Button>
          </Link>
          <Link href="/auth/signup">
            <Button variant="outline" size="lg" className="w-full sm:w-auto bg-transparent">
              Get Started
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
