{"name": "rapidgeniai-platform", "version": "1.0.0", "description": "RapidGeniAI - Low-Code/No-Code App Builder Platform", "main": "index.js", "scripts": {"install-all": "npm run install-backend && npm run install-frontend", "install-backend": "cd backend && npm install", "install-frontend": "cd frontend && npm install", "start": "npm run start-backend & npm run start-frontend", "start-backend": "cd backend && npm run dev", "start-frontend": "cd frontend && npm start", "build": "cd frontend && npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["lcnc", "low-code", "no-code", "app-builder", "drag-drop", "react", "nodejs", "express"], "author": "RapidGeniAI", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/rapidgeniai-platform.git"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}