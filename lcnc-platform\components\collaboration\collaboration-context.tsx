"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

interface User {
  id: string
  name: string
  email: string
  avatar?: string
  color: string
}

interface Cursor {
  userId: string
  x: number
  y: number
  user: User
}

interface Comment {
  id: string
  userId: string
  user: User
  content: string
  x: number
  y: number
  createdAt: string
  resolved: boolean
}

interface Version {
  id: string
  name: string
  createdAt: string
  createdBy: User
  description: string
}

interface CollaborationContextType {
  activeUsers: User[]
  cursors: Cursor[]
  comments: Comment[]
  versions: Version[]
  isCollaborating: boolean
  addComment: (content: string, x: number, y: number) => void
  resolveComment: (commentId: string) => void
  updateCursor: (x: number, y: number) => void
  inviteUser: (email: string) => Promise<boolean>
  createVersion: (name: string, description: string) => void
}

const CollaborationContext = createContext<CollaborationContextType | undefined>(undefined)

const mockUsers: User[] = [
  { id: "1", name: "<PERSON>", email: "<EMAIL>", color: "#3B82F6" },
  { id: "2", name: "<PERSON>", email: "<EMAIL>", color: "#EF4444" },
  { id: "3", name: "Carol Davis", email: "<EMAIL>", color: "#10B981" },
]

export function CollaborationProvider({ children }: { children: ReactNode }) {
  const [activeUsers, setActiveUsers] = useState<User[]>([mockUsers[1], mockUsers[2]])
  const [cursors, setCursors] = useState<Cursor[]>([])
  const [comments, setComments] = useState<Comment[]>([
    {
      id: "1",
      userId: "2",
      user: mockUsers[1],
      content: "This section needs more spacing",
      x: 300,
      y: 200,
      createdAt: new Date().toISOString(),
      resolved: false,
    },
  ])
  const [versions, setVersions] = useState<Version[]>([
    {
      id: "1",
      name: "Initial Design",
      createdAt: new Date(Date.now() - 86400000).toISOString(),
      createdBy: mockUsers[0],
      description: "First version of the landing page",
    },
    {
      id: "2",
      name: "Header Updates",
      createdAt: new Date(Date.now() - 43200000).toISOString(),
      createdBy: mockUsers[1],
      description: "Updated header design and navigation",
    },
  ])
  const [isCollaborating, setIsCollaborating] = useState(true)

  // Simulate real-time cursor updates
  useEffect(() => {
    const interval = setInterval(() => {
      setCursors((prev) =>
        activeUsers.map((user) => ({
          userId: user.id,
          x: Math.random() * 800 + 100,
          y: Math.random() * 600 + 100,
          user,
        })),
      )
    }, 3000)

    return () => clearInterval(interval)
  }, [activeUsers])

  const addComment = (content: string, x: number, y: number) => {
    const newComment: Comment = {
      id: Date.now().toString(),
      userId: "1", // Current user
      user: mockUsers[0],
      content,
      x,
      y,
      createdAt: new Date().toISOString(),
      resolved: false,
    }
    setComments((prev) => [...prev, newComment])
  }

  const resolveComment = (commentId: string) => {
    setComments((prev) => prev.map((comment) => (comment.id === commentId ? { ...comment, resolved: true } : comment)))
  }

  const updateCursor = (x: number, y: number) => {
    // In a real app, this would send cursor position to other users
    console.log("Cursor updated:", { x, y })
  }

  const inviteUser = async (email: string): Promise<boolean> => {
    // Mock invite functionality
    await new Promise((resolve) => setTimeout(resolve, 1000))
    return true
  }

  const createVersion = (name: string, description: string) => {
    const newVersion: Version = {
      id: Date.now().toString(),
      name,
      createdAt: new Date().toISOString(),
      createdBy: mockUsers[0],
      description,
    }
    setVersions((prev) => [newVersion, ...prev])
  }

  return (
    <CollaborationContext.Provider
      value={{
        activeUsers,
        cursors,
        comments,
        versions,
        isCollaborating,
        addComment,
        resolveComment,
        updateCursor,
        inviteUser,
        createVersion,
      }}
    >
      {children}
    </CollaborationContext.Provider>
  )
}

export function useCollaboration() {
  const context = useContext(CollaborationContext)
  if (context === undefined) {
    throw new Error("useCollaboration must be used within a CollaborationProvider")
  }
  return context
}
