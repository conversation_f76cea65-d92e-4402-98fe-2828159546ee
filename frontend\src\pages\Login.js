import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';

const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    const result = await login(formData);
    
    setLoading(false);
    
    if (!result.success) {
      // Error is already handled by the auth context with toast
      console.error('Login failed:', result.error);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center py-16 px-6 animate-fadeIn">
      <div className="max-w-lg w-full">
        {/* Header Section */}
        <div className="text-center mb-12 animate-slideIn">
          <div className="relative inline-block mb-8">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl blur-xl opacity-30 animate-pulse"></div>
            <div className="relative inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl shadow-2xl">
              <span className="text-white font-bold text-3xl">RG</span>
            </div>
          </div>
          <h1 className="text-5xl font-black bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent mb-4 leading-tight">
            RapidGeniAI
          </h1>
          <h2 className="text-2xl text-white/90 mb-4 font-light">
            Welcome back
          </h2>
          <p className="text-white/70 text-lg">
            Sign in to continue building amazing applications
          </p>
          <div className="mt-6 flex justify-center">
            <div className="h-1 w-24 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
          </div>
        </div>

        {/* Login Form */}
        <div className="relative animate-slideIn" style={{ animationDelay: '0.3s' }}>
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-xl"></div>
          <div className="relative card border-2 border-white/20 hover:border-white/30 transition-all duration-300">
            <form className="space-y-8" onSubmit={handleSubmit}>
              <div className="space-y-6">
                <div className="form-group">
                  <label htmlFor="username" className="form-label text-gray-700 text-base font-semibold">
                    Username or Email
                  </label>
                  <input
                    id="username"
                    name="username"
                    type="text"
                    required
                    className="form-input text-base py-4 px-5 border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500 rounded-xl"
                    placeholder="Enter your username or email"
                    value={formData.username}
                    onChange={handleChange}
                    disabled={loading}
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="password" className="form-label text-gray-700 text-base font-semibold">
                    Password
                  </label>
                  <input
                    id="password"
                    name="password"
                    type="password"
                    required
                    className="form-input text-base py-4 px-5 border-2 border-gray-200 hover:border-blue-300 focus:border-blue-500 rounded-xl"
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={handleChange}
                    disabled={loading}
                  />
                </div>
              </div>

              <div className="pt-4">
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full btn btn-primary py-5 text-lg font-bold shadow-2xl hover:shadow-3xl transform hover:scale-105 transition-all duration-300"
                >
                  {loading ? (
                    <>
                      <LoadingSpinner size="small" />
                      Signing in...
                    </>
                  ) : (
                    'Sign In to Dashboard'
                  )}
                </button>
              </div>

              <div className="text-center pt-6 border-t border-gray-100">
                <p className="text-base text-gray-600">
                  Don't have an account?{' '}
                  <Link
                    to="/register"
                    className="font-bold text-blue-600 hover:text-blue-500 transition-colors hover:underline"
                  >
                    Create one here
                  </Link>
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
