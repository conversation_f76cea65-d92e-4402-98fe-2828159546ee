"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Users, Share, History, MessageCircle, Plus, Clock } from "lucide-react"
import { useCollaboration } from "./collaboration-context"

export function CollaborationPanel() {
  const { activeUsers, comments, versions, inviteUser, createVersion } = useCollaboration()
  const [inviteEmail, setInviteEmail] = useState("")
  const [versionForm, setVersionForm] = useState({ name: "", description: "" })
  const [isInviting, setIsInviting] = useState(false)

  const handleInvite = async () => {
    if (!inviteEmail.trim()) return

    setIsInviting(true)
    try {
      await inviteUser(inviteEmail)
      setInviteEmail("")
      // Show success message
    } catch (error) {
      // Show error message
    } finally {
      setIsInviting(false)
    }
  }

  const handleCreateVersion = () => {
    if (versionForm.name.trim()) {
      createVersion(versionForm.name, versionForm.description)
      setVersionForm({ name: "", description: "" })
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString([], {
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    })
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="bg-gray-700 border-gray-600 text-white hover:bg-gray-600">
          <Users size={16} className="mr-2" />
          Collaborate
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Collaboration</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="users" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="users">
              <Users size={16} className="mr-2" />
              Users
            </TabsTrigger>
            <TabsTrigger value="comments">
              <MessageCircle size={16} className="mr-2" />
              Comments
            </TabsTrigger>
            <TabsTrigger value="versions">
              <History size={16} className="mr-2" />
              Versions
            </TabsTrigger>
            <TabsTrigger value="share">
              <Share size={16} className="mr-2" />
              Share
            </TabsTrigger>
          </TabsList>

          <TabsContent value="users" className="space-y-4">
            <div>
              <h3 className="font-medium mb-3">Active Users ({activeUsers.length})</h3>
              <div className="space-y-2">
                {activeUsers.map((user) => (
                  <div key={user.id} className="flex items-center gap-3 p-2 rounded-lg bg-gray-50">
                    <div
                      className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                      style={{ backgroundColor: user.color }}
                    >
                      {user.name.charAt(0)}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-sm">{user.name}</div>
                      <div className="text-xs text-gray-500">{user.email}</div>
                    </div>
                    <div className="w-2 h-2 bg-green-500 rounded-full" title="Online" />
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="comments" className="space-y-4">
            <div>
              <h3 className="font-medium mb-3">Comments ({comments.filter((c) => !c.resolved).length})</h3>
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {comments
                  .filter((comment) => !comment.resolved)
                  .map((comment) => (
                    <div key={comment.id} className="p-3 rounded-lg bg-gray-50 border">
                      <div className="flex items-start gap-2 mb-2">
                        <div
                          className="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-medium"
                          style={{ backgroundColor: comment.user.color }}
                        >
                          {comment.user.name.charAt(0)}
                        </div>
                        <div className="flex-1">
                          <div className="font-medium text-sm">{comment.user.name}</div>
                          <div className="text-xs text-gray-500">{formatDate(comment.createdAt)}</div>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700">{comment.content}</p>
                    </div>
                  ))}
                {comments.filter((c) => !c.resolved).length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <MessageCircle size={32} className="mx-auto mb-2 opacity-50" />
                    <p>No active comments</p>
                    <p className="text-xs">Double-click on the canvas to add a comment</p>
                  </div>
                )}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="versions" className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Version History</h3>
              <Dialog>
                <DialogTrigger asChild>
                  <Button size="sm">
                    <Plus size={16} className="mr-2" />
                    Save Version
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Save New Version</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2">Version Name</label>
                      <Input
                        placeholder="e.g., Header redesign"
                        value={versionForm.name}
                        onChange={(e) => setVersionForm({ ...versionForm, name: e.target.value })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">Description</label>
                      <Textarea
                        placeholder="Describe the changes made..."
                        value={versionForm.description}
                        onChange={(e) => setVersionForm({ ...versionForm, description: e.target.value })}
                      />
                    </div>
                    <Button onClick={handleCreateVersion} className="w-full">
                      Save Version
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>

            <div className="space-y-2 max-h-64 overflow-y-auto">
              {versions.map((version) => (
                <div key={version.id} className="p-3 rounded-lg bg-gray-50 border">
                  <div className="flex items-start justify-between mb-2">
                    <div className="font-medium text-sm">{version.name}</div>
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Clock size={12} />
                      {formatDate(version.createdAt)}
                    </div>
                  </div>
                  <p className="text-xs text-gray-600 mb-2">{version.description}</p>
                  <div className="text-xs text-gray-500">by {version.createdBy.name}</div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="share" className="space-y-4">
            <div>
              <h3 className="font-medium mb-3">Invite Collaborators</h3>
              <div className="flex gap-2">
                <Input
                  placeholder="Enter email address"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  className="flex-1"
                />
                <Button onClick={handleInvite} disabled={isInviting}>
                  {isInviting ? "Inviting..." : "Invite"}
                </Button>
              </div>
            </div>

            <div>
              <h3 className="font-medium mb-3">Share Link</h3>
              <div className="flex gap-2">
                <Input value="https://lcnc-platform.com/project/abc123" readOnly className="flex-1 bg-gray-50" />
                <Button
                  variant="outline"
                  onClick={() => navigator.clipboard.writeText("https://lcnc-platform.com/project/abc123")}
                >
                  Copy
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-2">Anyone with this link can view and edit this project</p>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}
