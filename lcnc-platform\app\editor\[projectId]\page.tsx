"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { useProjects } from "@/components/projects/project-context"
import { CanvasStateProvider } from "@/components/editor/canvas-state-context"
import { VisualEditor } from "@/components/editor/visual-editor"

interface EditorPageProps {
  params: {
    projectId: string
  }
}

export default function EditorPage({ params }: EditorPageProps) {
  const router = useRouter()
  const { getProjectById, currentProject, setCurrentProject } = useProjects()
  const [project, setProject] = useState(null)

  useEffect(() => {
    const foundProject = getProjectById(params.projectId)
    if (foundProject) {
      setProject(foundProject)
      setCurrentProject(foundProject)
    } else {
      router.push("/dashboard")
    }
  }, [params.projectId, getProjectById, setCurrentProject, router])

  if (!project) {
    return (
      <div className="min-h-screen bg-gray-800 flex items-center justify-center">
        <div className="text-white">Loading editor...</div>
      </div>
    )
  }

  return (
    <CanvasStateProvider>
      <VisualEditor project={project} />
    </CanvasStateProvider>
  )
}
