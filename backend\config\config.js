module.exports = {
  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'rapidgeniai-super-secret-key-change-in-production',
    expiresIn: '24h'
  },
  
  // Server Configuration
  server: {
    port: process.env.PORT || 5000,
    env: process.env.NODE_ENV || 'development'
  },
  
  // Storage Configuration
  storage: {
    dataDir: './data',
    usersFile: './data/users.json',
    appsFile: './data/apps.json',
    componentsFile: './data/components.json'
  },
  
  // Security Configuration
  security: {
    saltRounds: 12,
    maxLoginAttempts: 5,
    lockoutTime: 15 * 60 * 1000 // 15 minutes
  }
};
